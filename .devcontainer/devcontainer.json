// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/cpp
{
	"name": "C++",
	"hostRequirements": {
		"cpus": 4, 
		"memory": "16gb",
		"storage": "32gb"
	},
	"build": {
		"dockerfile": "Dockerfile"
	},
	"features": {
		"ghcr.io/devcontainers-contrib/features/zsh-plugins:0": {},
		"ghcr.io/nils-geistmann/devcontainers-features/zsh:0": {},
		"ghcr.io/devcontainers/features/desktop-lite:1": {}
	},
	"forwardPorts": [6080],
  "portsAttributes": {
    "6080": {
      "label": "desktop"
    }
  },
	"customizations": {
		"vscode": {
			"extensions": [
				"jeff-hykin.better-cpp-syntax",
				"ms-vscode.cpptools-themes",
				"ms-vscode.cpptools",
				"xaver.clang-format",
				"llvm-vs-code-extensions.vscode-clangd",
				"josetr.cmake-language-support-vscode",
				"ms-vscode.cmake-tools",
				"streetsidesoftware.code-spell-checker",
				"vadimcn.vscode-lldb",
				"yzhang.markdown-all-in-one",
				"stkb.rewrap"
			]
		},
		"settings": {
			"C_Cpp.intelliSenseEngine": "disabled",
  		"clangd.path": "/usr/bin/clangd"
		}
	},
	"postCreateCommand": "bash .devcontainer/post-create.sh",
	"runArgs": ["--shm-size=1g"]
}
