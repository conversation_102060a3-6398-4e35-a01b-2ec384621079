import os

# copyright text for source files
copyright_text_source = """/**
 * Copyright (C) 2021-2024 Saturneric <<EMAIL>>
 *
 * This file is part of GpgFrontend.
 *
 * GpgFrontend is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * GpgFrontend is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GpgFrontend. If not, see <https://www.gnu.org/licenses/>.
 *
 * The initial version of the source code is inherited from
 * the gpg4usb project, which is under GPL-3.0-or-later.
 *
 * All the source code of GpgFrontend was modified and released by
 * <PERSON><PERSON> <<EMAIL>> starting on May 12, 2021.
 *
 * SPDX-License-Identifier: GPL-3.0-or-later
 *
 */"""

# copyright text for script files
copyright_text_script = """# <AUTHOR> <EMAIL>
#
# This file is part of GpgFrontend.
#
# GpgFrontend is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# GpgFrontend is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with GpgFrontend. If not, see <https://www.gnu.org/licenses/>.
#
# The initial version of the source code is inherited from
# the gpg4usb project, which is under GPL-3.0-or-later.
#
# All the source code of GpgFrontend was modified and released by
# <AUTHOR> <EMAIL> starting on May 12, 2021.
#
# SPDX-License-Identifier: GPL-3.0-or-later
"""


def check_copright_by_path(path, copyright_text, suffix):
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(suffix):
                file_path = os.path.join(root, file)
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                if copyright_text not in content:
                    print(f"copyright declaration missing: {file_path}")


check_copright_by_path("src", copyright_text_source, (".c", ".cpp", ".h", ".hpp"))
check_copright_by_path("src", copyright_text_script, (".txt"))
check_copright_by_path("src", copyright_text_script, (".txt"))
check_copright_by_path("modules", copyright_text_source, (".c", ".cpp", ".h", ".hpp"))
check_copright_by_path("modules", copyright_text_script, (".txt"))


print("copyright check done")
