<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_TW">
<context>
    <name>EMailMetaDataDialog</name>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="20"/>
        <source>Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="54"/>
        <source>From</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="77"/>
        <source>To</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="100"/>
        <location filename="../EMailMetaDataDialog.ui" line="207"/>
        <source>CC</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="123"/>
        <location filename="../EMailMetaDataDialog.ui" line="214"/>
        <source>BCC</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="153"/>
        <source>Subject</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="175"/>
        <source>Tips: You can fill in multiple email addresses, please separate them with &quot;;&quot;, except for the &apos;From&apos; field.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="234"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.ui" line="241"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="183"/>
        <source>The &apos;From&apos; field cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="185"/>
        <source>The &apos;From&apos; field must contain a valid email address.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="189"/>
        <source>The &apos;To&apos; field cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="193"/>
        <source>One or more &apos;To&apos; addresses are invalid. Please separate multiple addresses with &quot;;&quot;.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="200"/>
        <source>One or more &apos;CC&apos; addresses are invalid. Please separate multiple addresses with &quot;;&quot;.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="208"/>
        <source>One or more &apos;BCC&apos; addresses are invalid. Please separate multiple addresses with &quot;;&quot;.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../EMailMetaDataDialog.cpp" line="214"/>
        <source>The &apos;Subject&apos; field cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
