//
// VMime library (http://www.vmime.org)
// Copyright (C) 2002 <PERSON> <<EMAIL>>
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License as
// published by the Free Software Foundation; either version 3 of
// the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
// General Public License for more details.
//
// You should have received a copy of the GNU General Public License along
// with this program; if not, write to the Free Software Foundation, Inc.,
// 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
//
// Linking this library statically or dynamically with other modules is making
// a combined work based on this library.  Thus, the terms and conditions of
// the GNU General Public License cover the whole combination.
//

#include "vmime/config.hpp"


#if VMIME_HAVE_MESSAGING_FEATURES && VMIME_HAVE_MESSAGING_PROTO_IMAP


#include "vmime/net/imap/IMAPMessageStructure.hpp"
#include "vmime/net/imap/IMAPMessagePart.hpp"


namespace vmime {
namespace net {
namespace imap {


IMAPMessageStructure::IMAPMessageStructure() {
}


IMAPMessageStructure::IMAPMessageStructure(const IMAPParser::body* body) {

	m_parts.push_back(IMAPMessagePart::create(null, 0, body));
}


IMAPMessageStructure::IMAPMessageStructure(
	const shared_ptr <IMAPMessagePart>& parent,
	const std::vector <std::unique_ptr <IMAPParser::body>>& list
) {

	size_t number = 0;

	for (auto it = list.begin() ; it != list.end() ; ++it, ++number) {
		m_parts.push_back(IMAPMessagePart::create(parent, number, it->get()));
	}
}


shared_ptr <const messagePart> IMAPMessageStructure::getPartAt(const size_t x) const {

	return m_parts[x];
}


shared_ptr <messagePart> IMAPMessageStructure::getPartAt(const size_t x) {

	return m_parts[x];
}


size_t IMAPMessageStructure::getPartCount() const {

	return m_parts.size();
}


// static
shared_ptr <IMAPMessageStructure> IMAPMessageStructure::emptyStructure() {

	static shared_ptr <IMAPMessageStructure> emptyStructure = make_shared <IMAPMessageStructure>();
	return emptyStructure;
}


} // imap
} // net
} // vmime


#endif // VMIME_HAVE_MESSAGING_FEATURES && VMIME_HAVE_MESSAGING_PROTO_IMAP

