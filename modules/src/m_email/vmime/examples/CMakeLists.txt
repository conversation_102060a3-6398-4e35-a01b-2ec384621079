
IF(VMIME_BUILD_SAMPLES)

	ADD_SUBDIRECTORY(viewer)

	FILE(
		GLOB
		VMIME_SAMPLES_SRC_FILES
		${CMAKE_SOURCE_DIR}/examples/*.cpp
	)

	# Build one file for each sample
	FOREACH(VMIME_SAMPLE_SRC_FILE ${VMIME_SAMPLES_SRC_FILES})

		GET_FILENAME_COMPONENT(VMIME_SAMPLE_NAME "${VMIME_SAMPLE_SRC_FILE}" NAME_WE)

		ADD_EXECUTABLE(
			${VMIME_SAMPLE_NAME}
			${VMIME_SAMPLE_SRC_FILE}
		)

		TARGET_LINK_LIBRARIES(
			${VMIME_SAMPLE_NAME}
			${VMIME_LIBRARY_NAME}
		)

		ADD_DEPENDENCIES(
			${VMIME_SAMPLE_NAME}
			${VMIME_LIBRARY_NAME}
		)

	END<PERSON><PERSON><PERSON>CH()

ELSE()

	MESSAGE(FATAL_ERROR "Examples are not to be built (set VMIME_BUILD_SAMPLES to YES.")

ENDIF()
