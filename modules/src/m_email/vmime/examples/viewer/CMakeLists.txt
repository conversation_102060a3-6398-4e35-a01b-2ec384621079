
IF(VMIME_BUILD_SAMPLES)

	FIND_PACKAGE(PkgConfig REQUIRED)
	PKG_CHECK_MODULES(GTK3 REQUIRED gtk+-3.0)

	INCLUDE_DIRECTORIES(${GTK3_INCLUDE_DIRS})
	LINK_DIRECTORIES(${GTK3_LIBRARY_DIRS})
	ADD_DEFINITIONS(${GTK3_CFLAGS_OTHER})

	ADD_EXECUTABLE(
		viewer
		viewer.cpp
	)

	TARGET_LINK_LIBRARIES(
		viewer
		${VMIME_LIBRARY_NAME}
		${GTK3_LIBRARIES}
	)

	ADD_DEPENDENCIES(
		viewer
		${VMIME_LIBRARY_NAME}
	)

ELSE()

	MESSAGE(FATAL_ERROR "Examples are not to be built (set VMIME_BUILD_SAMPLES to YES.")

ENDIF()
