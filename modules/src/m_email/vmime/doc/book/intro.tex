\chapter{Introduction}

% ============================================================================
\section{Overview}

VMime is a powerful C++ class library for working with MIME messages and
Internet messaging services like IMAP, POP or SMTP.

With VMime you can parse, generate and modify messages, and also connect to
store and transport services to receive or send messages over the Internet.
The library offers all the features to build a complete mail client.

The main objectives of this library are:

\begin{itemize}
\item fully RFC-compliant implementation;
\item object-oriented and modular design;
\item very easy-to-use (intuitive design);
\item well documented code;
\item very high reliability;
\item maximum portability.
\end{itemize}


% ============================================================================
\section{Features}

\noindent MIME features:

\begin{itemize}
\item Full support for RFC-2822 and multipart messages (RFC-1521)
\item Aggregate documents (MHTML) and embedded objects (RFC-2557)
\item Message Disposition Notification (RFC-3798)
\item 8-bit MIME (RFC-2047)
\item Encoded word extensions (RFC-2231)
\item Attachments
\end{itemize}

\noindent Network features:

\begin{itemize}
\item Support for IMAP, POP3 and maildir stores
\item Support for SMTP and sendmail transport methods
\item Extraction of whole message or specific parts
\item TLS/SSL security layer
\item SASL authentication
\end{itemize}


% ============================================================================
\section{Copyright and license}

VMime library is Free Software and is licensed under the terms of the GNU
General Public License\footnote{See Appendix \ref{appendix_license} and
\url{http://www.gnu.org/copyleft/gpl.html}} (GPL) version 3:

\begin{verbatim}
   Copyright (C) 2002 Vincent Richard

   VMime library is free software; you can redistribute it and/or
   modify it under the terms of the GNU General Public License as
   published by the Free Software Foundation; either version 3 of
   the License, or (at your option) any later version.

   VMime is distributed in the hope that it will be useful, but
   WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
   GNU General Public License for more details.

   Linking this library statically or dynamically with other
   modules is making a combined work based on this library.
   Thus, the terms and conditions of the GNU General Public
   License cover the whole combination.
\end{verbatim}

\newpage
\noindent This document is released under the terms of the
GNU Free Documentation
License\footnote{See \url{http://www.gnu.org/copyleft/fdl.html}} (FDL):

\begin{verbatim}
   Copyright (C) 2004 Vincent Richard

   Permission is granted to copy, distribute and/or modify
   this document under the terms of the GNU Free Documentation
   License, Version 1.2 or any later version published by the
   Free Software Foundation; with no Invariant Sections, no
   Front-Cover Texts, and no Back-Cover Texts.
\end{verbatim}

