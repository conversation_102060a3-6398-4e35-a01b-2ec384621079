# <AUTHOR> <EMAIL>
#
# This file is part of GpgFrontend.
#
# GpgFrontend is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# GpgFrontend is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with GpgFrontend. If not, see <https://www.gnu.org/licenses/>.
#
# The initial version of the source code is inherited from
# the gpg4usb project, which is under GPL-3.0-or-later.
#
# All the source code of GpgFrontend was modified and released by
# <AUTHOR> <EMAIL> starting on May 12, 2021.
#
# SPDX-License-Identifier: GPL-3.0-or-later

name: Build Release (Qt5)

on:
  push:
    branches: [main]
    paths-ignore:
      - "resource/lfs/locale/**"
      - "**.md"
  pull_request:
    branches: [main]
    paths-ignore:
      - "resource/lfs/locale/**"
      - "**.md"

env:
  BUILD_TYPE: Release
  GNUPG_VERSION: "2.4.8"

jobs:
  build:
    strategy:
      matrix:
        os: ["ubuntu-22.04", "windows-2022"]
    runs-on: ${{ matrix.os }}
    continue-on-error: true
    steps:
      - name: Set git to use LF(Windows) or CRLF(MacOS) line endings
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
        if: runner.os == 'Windows'

      - uses: actions/checkout@v4
        with:
          lfs: "false"
          submodules: recursive

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: release-qt5-${{ github.job }}-${{ matrix.os }}

      - name: Get Short SHA of Commit
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

      - name: Set up MinGW (Windows)
        uses: msys2/setup-msys2@v2
        with:
          update: false
          release: false
          cache: true
          install: >-
            git
            zip
            unzip
            msys2-devel
            base-devel
            msys2-runtime-devel 
            mingw-w64-x86_64-gcc
            mingw-w64-x86_64-make
            mingw-w64-x86_64-cmake
            mingw-w64-x86_64-qt5
            mingw-w64-x86_64-ninja 
            mingw-w64-x86_64-libarchive
            mingw-w64-x86_64-gtest
            mingw-w64-x86_64-autotools
            mingw-w64-x86_64-angleproject
            mingw-w64-x86_64-texinfo
            mingw-w64-x86_64-libassuan
            mingw-w64-x86_64-ccache
        if: runner.os == 'Windows'

      - name: Install Dependence (Linux)
        run: |
          sudo apt-get update
          sudo apt-get -y install build-essential binutils git autoconf automake gettext texinfo
          sudo apt-get -y install gcc g++ ninja-build
          sudo apt-get -y install libarchive-dev libssl-dev
          sudo apt-get -y install gpgsm libxcb-xinerama0 libxcb-icccm4-dev libcups2-dev libdrm-dev libegl1-mesa-dev
          sudo apt-get -y install libfuse2 libgcrypt20-dev libnss3-dev libpci-dev libpulse-dev libudev-dev libxtst-dev
          sudo apt-get -y install libglu1-mesa-dev libfontconfig1-dev libx11-xcb-dev libxcb-image0 gyp
          sudo apt-get -y install libglu1-mesa-dev libfontconfig1-dev libx11-xcb-dev libxcb-* libxkbcommon-x11-0
        if: runner.os == 'Linux'

      - name: Install Qt5
        uses: jurplel/install-qt-action@v4
        with:
          version: "5.15.2"
          cache: "true"
        if: runner.os == 'Linux'

      - name: Build GpgME (Windows)
        shell: msys2 {0}
        run: |
          cd $(cygpath -u "${{github.workspace}}")
          cd third_party/gpgme
          export CC="ccache gcc"
          export CXX="ccache g++"
          export CFLAGS="${CFLAGS} -Wno-int-conversion -Wno-incompatible-pointer-types"
          ./autogen.sh
          mkdir build && cd build
          ../configure --enable-maintainer-mode \
          --enable-static \
          --disable-shared \
          --enable-silent-rules \
          --disable-dependency-tracking \
          --enable-languages=cl \
          --disable-gpgconf-test \
          --disable-gpg-test \
          --disable-gpgsm-test \
          --disable-g13-test \
          --enable-w32-glib
          make -j4
          make install
        if: runner.os == 'Windows'

      - name: Build OpenSSL (Linux)
        run: |
          cd ${{github.workspace}}/third_party/openssl

          export CC="ccache gcc"
          export CXX="ccache g++"
          ./config
          make -j$(nproc)
          sudo make install_sw

          export LD_LIBRARY_PATH=/usr/local/lib:/usr/local/lib64:$LD_LIBRARY_PATH
          which openssl
          ldd $(which openssl) | grep ssl
          openssl version -a
          openssl list -kdf-algorithms || true
        if: runner.os == 'Linux'

      - name: Build googletest (Linux)
        run: |
          git clone --depth 1 --branch v1.15.2 https://github.com/google/googletest.git ${{github.workspace}}/third_party/googletest
          cd ${{github.workspace}}/third_party/googletest
          mkdir build && cd build
          cmake -G Ninja -DBUILD_SHARED_LIBS=ON ..
          ninja
          sudo ninja install
        if: runner.os == 'Linux'

      - name: Build GpgFrontend (Windows)
        shell: msys2 {0}
        run: |
          cd $(cygpath -u "${{github.workspace}}")
          mkdir build && cd build
          cmake -G Ninja -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} \
            -DGPGFRONTEND_QT5_BUILD=ON \
            -DGPGFRONTEND_LINK_GPGME_INTO_CORE=ON \
            ..
          cmake --build . --config ${{env.BUILD_TYPE}}  -- -j 4
        if: runner.os == 'Windows'

      - name: Build GpgFrontend (Linux)
        run: |
          export LD_LIBRARY_PATH=/usr/local/lib:/usr/local/lib64:$LD_LIBRARY_PATH
          cmake -B ${{github.workspace}}/build -G Ninja \
            -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} \
            -DGPGFRONTEND_QT5_BUILD=ON \
            -DGPGFRONTEND_BUILD_APP_IMAGE=ON \
            -DGPGFRONTEND_LINK_GPGME_INTO_CORE=ON
          cmake --build ${{github.workspace}}/build --config {{$env.BUILD_TYPE}} -- -v
        if: runner.os == 'Linux'

      - name: Package App Image (Linux)
        run: |
          mkdir ${{github.workspace}}/build/upload-artifact
          cd ${{github.workspace}}/build/upload-artifact
          wget -c -nv https://github.com/probonopd/linuxdeployqt/releases/download/continuous/linuxdeployqt-continuous-x86_64.AppImage
          chmod u+x linuxdeployqt-continuous-x86_64.AppImage
          export LD_LIBRARY_PATH=/usr/local/lib:/usr/local/lib64:$LD_LIBRARY_PATH
          ./linuxdeployqt-continuous-x86_64.AppImage \
            ${{github.workspace}}/build/artifacts/AppDir/usr/share/applications/*.desktop \
            -no-translations \
            -extra-plugins=iconengines,platforms \
            -appimage \
            -executable-dir=${{github.workspace}}/build/artifacts/AppDir/usr/lib/modules
          echo "BUILD_TYPE_LOWER=${BUILD_TYPE,,}" >> ${GITHUB_ENV}
          echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`" >> ${GITHUB_ENV}
        if: runner.os == 'Linux'

      - name: Generate Env Vars (Windows)
        run: |
          echo "SHORT_SHA=$("${{ github.sha }}".SubString(0, 8))" >> $env:GITHUB_ENV
          echo "BUILD_TYPE_LOWER=$("${{env.BUILD_TYPE}}".ToLower())" >> $env:GITHUB_ENV
        if: runner.os == 'Windows'

      - name: Download GnuPG Binary Release (Windows)
        shell: msys2 {0}
        run: |
          export URL="https://ftp.bktus.com/GnuPG/${{env.GNUPG_VERSION}}"
          export KEY_URL="https://ftp.bktus.com/GPG_KEYS/5CA3DA246843FD03.asc"
          export FILE="gnupg.zip"
          export CHECKSUM_FILE="SHA256SUMS.txt"
          export SIGNATURE_FILE="gnupg.zip.sig"
          export GNUPGHOME=$(mktemp -d)

          cd $(cygpath -u "${{github.workspace}}")

          mkdir -p build/downloads

          curl -o build/downloads/$FILE $URL/$FILE
          curl -o build/downloads/$CHECKSUM_FILE $URL/$CHECKSUM_FILE
          curl -o build/downloads/5CA3DA246843FD03.asc $KEY_URL
          curl -o build/downloads/$SIGNATURE_FILE $URL/$SIGNATURE_FILE

          gpg --import build/downloads/5CA3DA246843FD03.asc
          gpg --verify build/downloads/$SIGNATURE_FILE build/downloads/$FILE
          if [ $? -ne 0 ]; then
            echo "GnuPG signature verification failed!" >&2
            exit 1
          fi

          CHECKSUM=$(grep "$FILE" build/downloads/$CHECKSUM_FILE | awk '{print $1}')
          ACTUAL_CHECKSUM=$(sha256sum build/downloads/$FILE | awk '{print $1}')
          echo "Expected Checksum: $CHECKSUM"
          echo "Actual Checksum: $ACTUAL_CHECKSUM"
          if [ "$CHECKSUM" != "$ACTUAL_CHECKSUM" ]; then
            echo "Checksum verification failed!" >&2
            exit 1
          fi

          mkdir -p build/artifacts
          unzip build/downloads/$FILE -d build/artifacts/
          ls -l build/artifacts/
        if: runner.os == 'Windows'

      - name: Package (Windows)
        shell: msys2 {0}
        run: |
          cd $(cygpath -u "${{github.workspace}}")
          cp PrivacyPolicy.md build/artifacts/
          cp README.md build/artifacts/
          cp SECURITY.md build/artifacts/
          cp TRANSLATORS build/artifacts/
          cp COPYING build/artifacts/
          cp gpgfrontend.ico build/artifacts/bin/
          rm -rf build/artifacts/bin/*.a
          touch build/artifacts/bin/ENV.ini
          echo "PortableMode=true" >> build/artifacts/bin/ENV.ini
          cd build
          windeployqt --no-translations --force ./artifacts/bin/libgf_core.dll
          windeployqt --no-translations --force ./artifacts/bin/libgf_ui.dll
          windeployqt --no-translations --force ./artifacts/bin/libgf_test.dll
          windeployqt --no-translations --force ./artifacts/bin/GpgFrontend.exe
          mkdir upload-artifact
          cd artifacts
          zip -r ../upload-artifact/GpgFrontend-${{env.SHORT_SHA}}-${{matrix.os}}-x86_64.zip *
        if: runner.os == 'Windows'

      - name: Upload Artifact(Linux)
        uses: actions/upload-artifact@v4
        with:
          name: gpgfrontend-qt5-${{matrix.os}}-${{env.BUILD_TYPE_LOWER}}-${{ env.SHORT_SHA }}
          path: ${{github.workspace}}/build/upload-artifact/Gpg_Frontend*.AppImage*
        if: runner.os == 'Linux'

      - name: Upload Artifact (Windows)
        uses: actions/upload-artifact@master
        with:
          name: gpgfrontend-${{matrix.os}}-${{env.BUILD_TYPE_LOWER}}-${{env.SHORT_SHA}}
          path: ${{github.workspace}}/build/upload-artifact/*
        if: runner.os == 'Windows'
