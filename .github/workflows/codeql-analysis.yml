# <AUTHOR> <EMAIL>
#
# This file is part of GpgFrontend.
#
# GpgFrontend is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# GpgFrontend is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with GpgFrontend. If not, see <https://www.gnu.org/licenses/>.
#
# The initial version of the source code is inherited from
# the gpg4usb project, which is under GPL-3.0-or-later.
#
# All the source code of GpgFrontend was modified and released by
# <AUTHOR> <EMAIL> starting on May 12, 2021.
#
# SPDX-License-Identifier: GPL-3.0-or-later

name: "Code<PERSON>"

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  schedule:
    - cron: "19 14 * * 2"

env:
  BUILD_TYPE: Debug

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: ["cpp"]

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: "false"
          submodules: recursive

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: codeql-${{ github.job }}-${{ matrix.os }}

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Install Dependence
        run: |
          sudo apt-get update
          sudo apt-get -y install build-essential binutils git autoconf automake gettext texinfo
          sudo apt-get -y install gcc g++ ninja-build
          sudo apt-get -y install libarchive-dev libgpgme-dev
          sudo apt-get -y install gpg

      - name: Build googletest
        run: |
          git clone --depth 1 --branch v1.15.0 https://github.com/google/googletest.git ${{github.workspace}}/third_party/googletest
          cd ${{github.workspace}}/third_party/googletest
          mkdir build && cd build
          cmake -G Ninja -DBUILD_SHARED_LIBS=ON ..
          ninja
          sudo ninja install

      - name: Install Qt
        uses: jurplel/install-qt-action@v4
        with:
          version: "6.7.2"
          cache: "true"

      - name: Build GpgFrontend (Linux)
        run: |
          cmake -B ${{github.workspace}}/build -G Ninja \
            -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} \
            -DGPGFRONTEND_LINK_GPGME_INTO_CORE=ON
          cmake --build ${{github.workspace}}/build --config {{$env.BUILD_TYPE}} -- -v
        if: runner.os == 'Linux'

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
