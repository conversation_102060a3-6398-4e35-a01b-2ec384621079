This is an **unstable nightly build** of GpgFrontend. It may contain new
features or fixes that are still being tested and is less stable than official
releases.

#### Why Nightly Releases?

Nightly builds give users early access to new features and urgent fixes, often
within days of request or report. This helps you get improvements faster,
instead of waiting for the next stable release.

#### Important Notes

- **Use at your own risk.** Not recommended for production environments.
- **Breaking changes may occur without notice.** Configurations and workflows
  might change unexpectedly.
- **Quick turnaround:** Issue fixes and feature requests on GitHub are often
  delivered in these nightly builds.
- **Thorough testing by users is highly encouraged!** Your feedback is essential
  for improving stability and quality before the next official release.
- Report problems or suggestions via GitHub Issues or contact the maintainer.

#### Security & Integrity

Nightly releases are created automatically. For security, my private signing key
is never uploaded to GitHub, so **these builds are not GPG-signed**.  
To verify integrity, please check the provided SHA256 checksums. If you need
extra assurance, feel free to reach out or wait for the next official signed
release.

Thank you for supporting GpgFrontend!
