# <AUTHOR> <EMAIL>
#
# This file is part of GpgFrontend.
#
# GpgFrontend is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# GpgFrontend is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with GpgFrontend. If not, see <https://www.gnu.org/licenses/>.
#
# The initial version of the source code is inherited from
# the gpg4usb project, which is under GPL-3.0-or-later.
#
# All the source code of GpgFrontend was modified and released by
# <AUTHOR> <EMAIL> starting on May 12, 2021.
#
# SPDX-License-Identifier: GPL-3.0-or-later

name: Develop CI Qt5

on:
  push:
    branches: ["develop", "dev/**"]
    paths-ignore:
      - "resource/lfs/locale/**"
      - "**.md"

env:
  BUILD_TYPE: Debug

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: "false"
          submodules: recursive

      - name: Install Dependence
        run: |
          sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
          sudo apt-get update
          sudo apt-get -y install build-essential binutils git cmake
          sudo apt-get -y install autoconf automake gettext texinfo
          sudo apt-get -y install gcc g++ ninja-build gnupg qtbase5-dev qttools5-dev
          sudo apt-get -y install libarchive-dev libssl-dev libgpgme-dev libfuse2

      - name: Build googletest
        run: |
          git clone --depth 1 --branch v1.15.0 https://github.com/google/googletest.git ${{github.workspace}}/third_party/googletest
          cd ${{github.workspace}}/third_party/googletest
          mkdir build && cd build
          cmake -G Ninja -DBUILD_SHARED_LIBS=ON ..
          ninja
          sudo ninja install

      - name: Build GpgFrontend
        run: |
          cmake -B ${{github.workspace}}/build -G Ninja \
            -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} \
            -DGPGFRONTEND_BUILD_APP_IMAGE=ON \
            -DGPGFRONTEND_QT5_BUILD=ON \
            -DGPGFRONTEND_LINK_GPGME_INTO_CORE=ON
          cmake --build ${{github.workspace}}/build --config {{$env.BUILD_TYPE}} -- -v

      - name: Package App Image
        run: |
          mkdir ${{github.workspace}}/build/final-artifact
          cd ${{github.workspace}}/build/final-artifact
          wget -O linuxdeployqt.AppImage -c -nv https://ftp.bktus.com/linuxdeployqt/linuxdeployqt-01052025-x86_64.AppImage
          chmod u+x linuxdeployqt.AppImage
          mkdir -p ${{github.workspace}}/build/artifacts/AppDir/usr/share/doc/libc6
          touch ${{github.workspace}}/build/artifacts/AppDir/usr/share/doc/libc6/copyright
          ./linuxdeployqt.AppImage ${{github.workspace}}/build/artifacts/AppDir/usr/share/applications/*.desktop -no-translations -extra-plugins=iconengines,platforms/libqoffscreen.so -appimage -unsupported-allow-new-glibc
          echo "BUILD_TYPE_LOWER=${BUILD_TYPE,,}" >> ${GITHUB_ENV}
          echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`" >> ${GITHUB_ENV}

      - name: Run Unit Tests
        run: |
          cd ${{github.workspace}}/build/final-artifact
          export QT_QPA_PLATFORM=offscreen
          export GTEST_OUTPUT=json:/workspace/GpgFrontend/GpgFrontend/build/final-artifact/unit_tests.json
          ./Gpg_Frontend-*-x86_64.AppImage -t || true

      - name: Upload Artifact
        uses: actions/upload-artifact@v3
        with:
          name: gpgfrontend-${{env.BUILD_TYPE_LOWER}}-${{env.SHORT_SHA}}
          path: |
            ${{github.workspace}}/build/final-artifact/Gpg_Frontend*.AppImage*
            ${{github.workspace}}/build/final-artifact/unit_tests.json
