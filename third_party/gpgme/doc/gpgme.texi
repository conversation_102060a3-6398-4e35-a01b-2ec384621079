\input texinfo                   @c -*- mode: texinfo; coding: utf-8; -*-
@documentencoding UTF-8
@setfilename gpgme.info
@include defs.inc
@settitle The `GnuPG Made Easy' Reference Manual

@dircategory GNU Libraries
@direntry
* @acronym{GPGME}: (gpgme).          Adding support for cryptography to your program.
@end direntry

@c Unify some of the indices.
@syncodeindex tp fn
@syncodeindex pg fn

@copying
Copyright @copyright{} 2002--2008, 2010, 2012--2018 g10 Code GmbH.

@quotation
Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU General Public License as published by the
Free Software Foundation; either version 3 of the License, or (at your
option) any later version. The text of the license can be found in the
section entitled ``Copying''.
@end quotation

This document is distributed in the hope that it will be useful, but
WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
General Public License for more details.
@end copying

@c Macros used by the description of the UI server protocol
@macro clnt{string}
  @sc{c:} \string\
@end macro
@macro srvr{string}
  @sc{s:} \string\
@end macro

@c API version.
@macro since{string}
  @sc{Since:} \string\
@end macro


@c
@c  T I T L E  P A G E
@c
@ifinfo
This file documents the @acronym{GPGME} library.

This is Edition @value{EDITION}, last updated @value{UPDATED}, of
@cite{The `GnuPG Made Easy' Reference Manual}, for Version
@value{VERSION}.

@c NOTE: Don't forget to update the year for the TeX version, too.
@insertcopying

@end ifinfo

@c We do not want that bastard short titlepage.
@c @iftex
@c @shorttitlepage The `GnuPG Made Easy' Reference Manual
@c @end iftex
@titlepage
@center @titlefont{The `GnuPG Made Easy'}
@sp 1
@center @titlefont{Reference Manual}
@sp 6
@center Edition @value{EDITION}
@sp 1
@center last updated @value{UPDATED}
@sp 1
@center for version @value{VERSION}
@page
@vskip 0pt plus 1filll
Published by The GnuPG Project@* c/o g10 Code GmbH@* Hüttenstr. 61@* 40699 Erkrath, Germany

@insertcopying
@end titlepage
@page

@summarycontents
@contents

@ifnottex
@node Top
@top Main Menu
This is Edition @value{EDITION}, last updated @value{UPDATED}, of
@cite{The `GnuPG Made Easy' Reference Manual}, for Version
@value{VERSION} of the @acronym{GPGME} library.
@end ifnottex

@menu
* Introduction::                  How to use this manual.
* Preparation::                   What you should do before using the library.
* Protocols and Engines::         Supported crypto protocols.
* Algorithms::                    Supported algorithms.
* Error Handling::                Error numbers and their meanings.
* Exchanging Data::               Passing data to and from @acronym{GPGME}.
* Contexts::                      Handling @acronym{GPGME} contexts.

Appendices

* UI Server Protocol::            The GnuPG UI Server Protocol.
* Debugging::                     How to solve problems.
* Deprecated Functions::          Documentation of deprecated functions.

* Library Copying::               The GNU Lesser General Public License says
                                  how you can copy and share `GnuPG Made Easy'.
* Copying::                       The GNU General Public License says how you
                                  can copy and share this manual.

Indices

* Concept Index::                 Index of concepts and programs.
* Function and Data Index::       Index of functions, variables and data types.

@detailmenu
 --- The Detailed Node Listing ---

Introduction

* Getting Started::               Purpose of the manual, and how to use it.
* Features::                      Reasons to install and use @acronym{GPGME}.
* Overview::                      Basic architecture of the @acronym{GPGME} library.

Preparation

* Header::                        What header file you need to include.
* Building the Source::           Compiler options to be used.
* Largefile Support (LFS)::       How to use @acronym{GPGME} with LFS.
* Using Automake::                Compiler options to be used the easy way.
* Using Libtool::                 Avoiding compiler options entirely.
* Library Version Check::         Getting and verifying the library version.
* Signal Handling::               How @acronym{GPGME} affects signal handling.
* Multi-Threading::               How @acronym{GPGME} can be used in an MT environment.

Protocols and Engines

* Engine Version Check::          Verifying the engine version.
* Engine Information::            Obtaining more information about the engines.
* Engine Configuration::          Changing the engine configuration.
* OpenPGP::                       Support for the OpenPGP protocol.
* Cryptographic Message Syntax::  Support for the CMS.

Algorithms

* Public Key Algorithms::         A list of all public key algorithms.
* Hash Algorithms::               A list of all hash algorithms.

Error Handling

* Error Values::                  The error value and what it means.
* Error Codes::                   A list of important error codes.
* Error Sources::                 A list of important error sources.
* Error Strings::                 How to get a descriptive string from a value.

Exchanging Data

* Creating Data Buffers::         Creating new data buffers.
* Destroying Data Buffers::       Releasing data buffers.
* Manipulating Data Buffers::     Operations on data buffers.

Creating Data Buffers

* Memory Based Data Buffers::     Creating memory based data buffers.
* File Based Data Buffers::       Creating file based data buffers.
* Callback Based Data Buffers::   Creating callback based data buffers.

Manipulating Data Buffers

* Data Buffer I/O Operations::    I/O operations on data buffers.
* Data Buffer Meta-Data::         Meta-data manipulation of data buffers.
* Data Buffer Convenience::       Convenience function for data buffers.

Contexts

* Creating Contexts::             Creating new @acronym{GPGME} contexts.
* Destroying Contexts::           Releasing @acronym{GPGME} contexts.
* Result Management::             Managing the result of crypto operations.
* Context Attributes::            Setting properties of a context.
* Key Management::                Managing keys with @acronym{GPGME}.
* Crypto Operations::             Using a context for cryptography.
* Miscellaneous::                 Miscellaneous operations.
* Run Control::                   Controlling how operations are run.

Context Attributes

* Protocol Selection::            Selecting the protocol used by a context.
* Crypto Engine::                 Configuring the crypto engine.
* Setting the Sender::            How to tell the engine the sender.
* ASCII Armor::                   Requesting @acronym{ASCII} armored output.
* Text Mode::                     Choosing canonical text mode.
* Offline Mode::                  Choosing offline mode.
* Included Certificates::         Including a number of certificates.
* Key Listing Mode::              Selecting key listing mode.
* Passphrase Callback::           Getting the passphrase from the user.
* Progress Meter Callback::       Being informed about the progress.
* Status Message Callback::       Status messages received from gpg.
* Locale::                        Setting the locale of a context.

Key Management

* Key objects::                   Description of the key structures.
* Listing Keys::                  Browsing the list of available keys.
* Information About Keys::        Requesting detailed information about keys.
* Manipulating Keys::             Operations on keys.
* Generating Keys::               Creating new key pairs.
* Signing Keys::                  Adding key signatures to public keys.
* Exporting Keys::                Retrieving key data from the key ring.
* Importing Keys::                Adding keys to the key ring.
* Deleting Keys::                 Removing keys from the key ring.
* Changing Passphrases::          Change the passphrase of a key.
* Changing TOFU Data::            Changing data pertaining to TOFU.
* Advanced Key Editing::          Advanced key edit operation.

Crypto Operations

* Decrypt::                       Decrypting a ciphertext.
* Verify::                        Verifying a signature.
* Decrypt and Verify::            Decrypting a signed ciphertext.
* Sign::                          Creating a signature.
* Encrypt::                       Encrypting a plaintext.
* Random::                        Getting strong random bytes.

Sign

* Selecting Signers::             How to choose the keys to sign with.
* Creating a Signature::          How to create a signature.
* Signature Notation Data::       How to add notation data to a signature.

Encrypt

* Encrypting a Plaintext::        How to encrypt a plaintext.

Miscellaneous

* Running other Programs::        Running other Programs.
* Using the Assuan protocol::     Using the Assuan protocol.
* Checking for updates::          How to check for software updates.

Run Control

* Waiting For Completion::        Waiting until an operation is completed.
* Using External Event Loops::    Advanced control over what happens when.
* Cancellation::                  How to end pending operations prematurely.

Using External Event Loops

* I/O Callback Interface::        How I/O callbacks are registered.
* Registering I/O Callbacks::     How to use I/O callbacks for a context.
* I/O Callback Example::          An example how to use I/O callbacks.
* I/O Callback Example GTK+::     How to integrate @acronym{GPGME} in GTK+.
* I/O Callback Example GDK::      How to integrate @acronym{GPGME} in GDK.
* I/O Callback Example Qt::       How to integrate @acronym{GPGME} in Qt.

@end detailmenu
@end menu

@node Introduction
@chapter Introduction

`GnuPG Made Easy' (@acronym{GPGME}) is a C language library that
allows to add support for cryptography to a program.  It is designed
to make access to public key crypto engines like GnuPG or GpgSM easier
for applications.  @acronym{GPGME} provides a high-level crypto API
for encryption, decryption, signing, signature verification and key
management.

@acronym{GPGME} uses GnuPG and GpgSM as its backends to support
OpenPGP and the Cryptographic Message Syntax (CMS).

@menu
* Getting Started::               Purpose of the manual, and how to use it.
* Features::                      Reasons to install and use @acronym{GPGME}.
* Overview::                      Basic architecture of the @acronym{GPGME} library.
@end menu


@node Getting Started
@section Getting Started

This manual documents the @acronym{GPGME} library programming
interface.  All functions and data types provided by the library are
explained.

The reader is assumed to possess basic knowledge about cryptography in
general, and public key cryptography in particular.  The underlying
cryptographic engines that are used by the library are not explained,
but where necessary, special features or requirements by an engine are
mentioned as far as they are relevant to @acronym{GPGME} or its users.

This manual can be used in several ways.  If read from the beginning
to the end, it gives a good introduction into the library and how it
can be used in an application.  Forward references are included where
necessary.  Later on, the manual can be used as a reference manual to
get just the information needed about any particular interface of the
library.  Experienced programmers might want to start looking at the
examples at the end of the manual, and then only read up those parts
of the interface which are unclear.

The documentation for the language bindings is currently not included
in this manual.  Those languages bindings follow the general
programming model of @acronym{GPGME} but may provide some extra high
level abstraction on top of the @acronym{GPGME} style API.  For now
please see the README files in the @file{lang/} directory of the
source distribution.


@node Features
@section Features

@acronym{GPGME} has a couple of advantages over other libraries doing
a similar job, and over implementing support for GnuPG or other crypto
engines into your application directly.

@table @asis
@item it's free software
Anybody can use, modify, and redistribute it under the terms of the GNU
Lesser General Public License (@pxref{Library Copying}).

@item it's flexible
@acronym{GPGME} provides transparent support for several cryptographic
protocols by different engines.  Currently, @acronym{GPGME} supports
the OpenPGP protocol using GnuPG as the backend, and the Cryptographic
Message Syntax using GpgSM as the backend.

@item it's easy
@acronym{GPGME} hides the differences between the protocols and
engines from the programmer behind an easy-to-use interface.  This way
the programmer can focus on the other parts of the program, and still
integrate strong cryptography in his application.  Once support for
@acronym{GPGME} has been added to a program, it is easy to add support
for other crypto protocols once @acronym{GPGME} backends provide them.

@item it's language friendly
@acronym{GPGME} comes with language bindings for several common
programming languages: Common Lisp, C++, and Python 3.  The C++
bindings and the Python bindings are available in separate
repositories.
@end table

@node Overview
@section Overview

@acronym{GPGME} provides a data abstraction that is used to pass data
to the crypto engine, and receive returned data from it.  Data can be
read from memory or from files, but it can also be provided by a
callback function.

The actual cryptographic operations are always set within a context.
A context provides configuration parameters that define the behaviour
of all operations performed within it.  Only one operation per context
is allowed at any time, but when one operation is finished, you can
run the next operation in the same context.  There can be more than
one context, and all can run different operations at the same time.

Furthermore, @acronym{GPGME} has rich key management facilities
including listing keys, querying their attributes, generating,
importing, exporting and deleting keys, and acquiring information
about the trust path.

With some precautions, @acronym{GPGME} can be used in a multi-threaded
environment, although it is not completely thread safe and thus needs
the support of the application.


@node Preparation
@chapter Preparation

To use @acronym{GPGME}, you have to perform some changes to your
sources and the build system.  The necessary changes are small and
explained in the following sections.  At the end of this chapter, it
is described how the library is initialized, and how the requirements
of the library are verified.

@menu
* Header::                        What header file you need to include.
* Building the Source::           Compiler options to be used.
* Largefile Support (LFS)::       How to use @acronym{GPGME} with LFS.
* Using Automake::                Compiler options to be used the easy way.
* Using Libtool::                 Avoiding compiler options entirely.
* Library Version Check::         Getting and verifying the library version.
* Signal Handling::               How @acronym{GPGME} affects signal handling.
* Multi-Threading::               How @acronym{GPGME} can be used in an MT environment.
@end menu


@node Header
@section Header
@cindex header file
@cindex include file

All interfaces (data types and functions) of the library are defined
in the header file `gpgme.h'.  You must include this in all programs
using the library, either directly or through some other header file,
like this:

@example
#include <gpgme.h>
@end example

The name space of @acronym{GPGME} is @code{gpgme_*} for function names
and data types and @code{GPGME_*} for other symbols.  Symbols internal
to @acronym{GPGME} take the form @code{_gpgme_*} and @code{_GPGME_*}.

Because @acronym{GPGME} makes use of the GPG Error library, using
@acronym{GPGME} will also use the @code{GPG_ERR_*} name space
directly, and the @code{gpg_err*}, @code{gpg_str*}, and @code{gpgrt_*}
name space indirectly.


@node Building the Source
@section Building the Source
@cindex compiler options
@cindex compiler flags

If you want to compile a source file including the `gpgme.h' header
file, you must make sure that the compiler can find it in the
directory hierarchy.  This is accomplished by adding the path to the
directory in which the header file is located to the compilers include
file search path (via the @option{-I} option).

However, the path to the include file is determined at the time the
source is configured.  To solve this problem, gpgme ships with
@code{gpgme.pc} file, that knows about the path to the include file
and other configuration options.  The command, @command{pkg-config},
can be used to handle information with @code{gpgme.pc} file.  In an
environment which doesn't have @command{pkg-config} (like the one in
early stage of OS bootstrap), for Automake, you can use
@code{gpgme.m4} which invokes @command{gpgrt-config} with
@code{gpgme.pc}.  (In the past, gpgme used to ship with a small helper
program @command{gpgme-config}.  This functionality of
@command{gpgme-config} is replaced by @command{pkg-config} with
@code{gpgme.pc} file.)

The options that need to be added to the compiler invocation at
compile time are output by the @option{--cflags} option to
@command{pkg-config gpgme}.  The following example shows how it can be
used at the command line:

@example
gcc -c foo.c `pkg-config --cflags gpgme`
@end example

Adding the output of @samp{pkg-config --cflags gpgme} to the compiler
command line will ensure that the compiler can find the
@acronym{GPGME} header file.

A similar problem occurs when linking the program with the library.
Again, the compiler has to find the library files.  For this to work,
the path to the library files has to be added to the library search
path (via the @option{-L} option).  For this, the option
@option{--libs} to @command{pkg-config gpgme} can be used.  For
convenience, this option also outputs all other options that are
required to link the program with @acronym{GPGME} (in particular, the
@samp{-lgpgme} option).  The example shows how to link @file{foo.o}
with the @acronym{GPGME} library to a program @command{foo}.

@example
gcc -o foo foo.o `pkg-config --libs gpgme`
@end example

Of course you can also combine both examples to a single command by
specifying both options to @command{pkg-config gpgme}:

@example
gcc -o foo foo.c `pkg-config --cflags --libs gpgme`
@end example

@c
@c Feature not available by pkg-config/gpgrt-config
@c
@c If you need to detect the installed language bindings you can use list
@c them using:
@c
@c @example
@c gpgme-config --print-lang
@c @end example
@c
@c or test for the availability using
@c
@c @example
@c gpgme-config --have-lang=cl && echo 'Bindings for Common Lisp available'
@c @end example
@c

@node Largefile Support (LFS)
@section Largefile Support (LFS)
@cindex largefile support
@cindex LFS

@acronym{GPGME} is compiled with largefile support by default, if it
is available on the system.  This means that GPGME supports files
larger than two gigabyte in size, if the underlying operating system
can.  On some systems, largefile support is already the default.  On
such systems, nothing special is required.  However, some systems
provide only support for files up to two gigabyte in size by default.
Support for larger file sizes has to be specifically enabled.

To make a difficult situation even more complex, such systems provide
two different types of largefile support.  You can either get all
relevant functions replaced with alternatives that are largefile
capable, or you can get new functions and data types for largefile
support added.  Those new functions have the same name as their
smallfile counterparts, but with a suffix of 64.

An example: The data type @code{off_t} is 32 bit wide on GNU/Linux PC
systems.  To address offsets in large files, you can either enable
largefile support add-on.  Then a new data type @code{off64_t} is
provided, which is 64 bit wide.  Or you can replace the existing
@code{off_t} data type with its 64 bit wide counterpart.  All
occurrences of @code{off_t} are then automagically replaced.

As if matters were not complex enough, there are also two different
types of file descriptors in such systems.  This is important because
if file descriptors are exchanged between programs that use a
different maximum file size, certain errors must be produced on some
file descriptors to prevent subtle overflow bugs from occurring.

As you can see, supporting two different maximum file sizes at the
same time is not at all an easy task.  However, the maximum file size
does matter for @acronym{GPGME}, because some data types it uses in
its interfaces are affected by that.  For example, the @code{off_t}
data type is used in the @code{gpgme_data_seek} function, to match its
@acronym{POSIX} counterpart.  This affects the call-frame of the
function, and thus the ABI of the library.  Furthermore, file
descriptors can be exchanged between GPGME and the application.

For you as the user of the library, this means that your program must
be compiled in the same file size mode as the library.  Luckily, there
is absolutely no valid reason for new programs to not enable largefile
support by default and just use that.  The compatibility modes (small
file sizes or dual mode) can be considered an historic artefact, only
useful to allow for a transitional period.

On POSIX platforms @acronym{GPGME} is compiled using largefile support
by default.  This means that your application must do the same, at
least as far as it is relevant for using the @file{gpgme.h} header
file.  All types in this header files refer to their largefile
counterparts, if they are different from any default types on the
system.

On 32 and 64 bit Windows platforms @code{off_t} is declared as 32 bit
signed integer.  There is no specific support for LFS in the C
library.  The recommendation from Microsoft is to use the native
interface (@code{CreateFile} et al.) for large files.  Released binary
versions of @acronym{GPGME} (libgpgme-11.dll) have always been build
with a 32 bit @code{off_t}.  To avoid an ABI break we stick to this
convention for 32 bit Windows by using @code{long} there.
@acronym{GPGME} versions for 64 bit Windows have never been released
and thus we are able to use @code{int64_t} instead of @code{off_t}
there.  For easier migration the typedef @code{gpgme_off_t} has been
defined.  The reason we cannot use @code{off_t} directly is that some
toolchains (e.g., mingw64) introduce a POSIX compatible hack for
@code{off_t}.  Some widely used toolkits make use of this hack and in
turn @acronym{GPGME} would need to use it also.  However, this would
introduce an ABI break and existing software making use of libgpgme
might suffer from a severe break.  Thus with version 1.4.2 we
redefined all functions using @code{off_t} to use @code{gpgme_off_t}
which is defined as explained above.  This way we keep the ABI well
defined and independent of any toolchain hacks.  The bottom line is
that LFS support in @acronym{GPGME} is only available on 64 bit
versions of Windows.

On POSIX platforms you can enable largefile support, if it is
different from the default on the system the application is compiled
on, by using the Autoconf macro @code{AC_SYS_LARGEFILE}.  If you do
this, then you don't need to worry about anything else: It will just
work.  In this case you might also want to use @code{AC_FUNC_FSEEKO}
to take advantage of some new interfaces, and @code{AC_TYPE_OFF_T}
(just in case).

If you do not use Autoconf, you can define the preprocessor symbol
@code{_FILE_OFFSET_BITS} to 64 @emph{before} including any header
files, for example by specifying the option
@code{-D_FILE_OFFSET_BITS=64} on the compiler command line.  You will
also want to define the preprocessor symbol @code{LARGEFILE_SOURCE} to
1 in this case, to take advantage of some new interfaces.

If you do not want to do either of the above, you probably know enough
about the issue to invent your own solution.  Just keep in mind that
the @acronym{GPGME} header file expects that largefile support is
enabled, if it is available.  In particular, we do not support dual
mode (@code{_LARGEFILE64_SOURCE}).


@node Using Automake
@section Using Automake
@cindex automake
@cindex autoconf

You can simply use @code{PKG_CHECK_MODULES} macro with @command{pkg-config}:
@example
PKG_CHECK_MODULES([GPGME], [gpgme >= 1.23.1])
@end example

Alternatively, instead of using @command{pkg-config}, for building on
an environment with no pkg-config, @acronym{GPGME} provides an
extension to Automake that does all the work for you.  Please note
that it is required to have gpgrt-config from libgpg-error installed
in this case.

@c A simple macro for optional variables.
@macro ovar{varname}
@r{[}@var{\varname\}@r{]}
@end macro
@defmac AM_PATH_GPGME (@ovar{minimum-version}, @ovar{action-if-found}, @ovar{action-if-not-found})
Check whether @acronym{GPGME} (at least version @var{minimum-version},
if given) exists on the host system.  If it is found, execute
@var{action-if-found}, otherwise do @var{action-if-not-found}, if
given.

This macro locates for @code{gpgme.pc}, with cross-compile support.

Additionally, the function defines @code{GPGME_CFLAGS} to the flags
needed for compilation of the program to find the @file{gpgme.h}
header file, and @code{GPGME_LIBS} to the linker flags needed to link
the program to the @acronym{GPGME} library.
@c
@c If the used helper script does not match the target type you are
@c building for a warning is printed and the string @code{gpgme} is
@c appended to the variable @code{gpg_config_script_warn}.
@c@c@c@c@c@c@c@c
@c ^-- About this statement.
@c In the past, this might happens, when it was configured by
@c --with-*-prefix options, and it located the helper script for
@c other target.  Cross-build by gpgrt-config has been improved
@c (in different way), so it's not relevant now.
@c


@code{AM_PATH_GPGME_PTHREAD} was provided to check for the version of
@acronym{GPGME} with the native pthread implementation, and it defined
@code{GPGME_PTHREAD_CFLAGS} and @code{GPGME_PTHREAD_LIBS}.  Since
version 1.8.0 this is no longer necessary, as @acronym{GPGME} itself
is thread safe.  Please use plain @code{AM_PATH_GPGME} instead, with
@code{GPGME_CFLAGS} and @code{GPGME_LDFLAGS}.

@end defmac

You can use the defined Autoconf variables like this in your
@file{Makefile.am}:

@example
AM_CPPFLAGS = $(GPGME_CFLAGS)
LDADD = $(GPGME_LIBS)
@end example


@node Using Libtool
@section Using Libtool
@cindex libtool

The easiest way is to just use GNU Libtool.  If you use libtool, and
link to @code{libgpgme.la}, everything will be done automatically by
Libtool.


@node Library Version Check
@section Library Version Check
@cindex version check, of the library

@deftypefun {const char *} gpgme_check_version (@w{const char *@var{required_version}})
The function @code{gpgme_check_version} has four purposes.  It can be
used to retrieve the version number of the library.  In addition it
can verify that the version number is higher than a certain required
version number.  In either case, the function initializes some
sub-systems, and for this reason alone it must be invoked early in
your program, before you make use of the other functions in
@acronym{GPGME}.  The last purpose is to run selftests.

As a side effect for W32 based systems, the socket layer will get
initialized.


If @var{required_version} is @code{NULL}, the function returns a
pointer to a statically allocated string containing the version number
of the library.

If @var{required_version} is not @code{NULL}, it should point to a
string containing a version number, and the function checks that the
version of the library is at least as high as the version number
provided.  In this case, the function returns a pointer to a
statically allocated string containing the version number of the
library.  If @var{REQUIRED_VERSION} is not a valid version number, or
if the version requirement is not met, the function returns
@code{NULL}.

If you use a version of a library that is backwards compatible with
older releases, but contains additional interfaces which your program
uses, this function provides a run-time check if the necessary
features are provided by the installed version of the library.

If a selftest fails, the function may still succeed.  Selftest errors
are returned later when invoking @code{gpgme_new} or
@code{gpgme-data_new}, so that a detailed error code can be returned
(historically, @code{gpgme_check_version} does not return a detailed
error code).
@end deftypefun


@deftypefun {int} gpgme_set_global_flag  @
            (@w{const char *@var{name}}, @
            @w{const char *@var{value}})

@since{1.4.0}

On some systems it is not easy to set environment variables and thus
hard to use @acronym{GPGME}'s internal trace facility for debugging.
This function has been introduced as an alternative way to enable
debugging and for a couple of other rarely used tweaks.  It is
important to assure that only one thread accesses @acronym{GPGME}
functions between a call to this function and after the return from
the call to @code{gpgme_check_version}.

All currently supported features require that this function is called
as early as possible --- even before @code{gpgme_check_version}.  The
features are identified by the following values for @var{name}:

@table @code
@item debug
To enable debugging use the string ``debug'' for @var{name} and
@var{value} identical to the value used with the environment variable
@code{GPGME_DEBUG}.

@item disable-gpgconf
Using this feature with any @var{value} disables the detection of the
gpgconf program and thus forces GPGME to fallback into the simple
OpenPGP only mode.  It may be used to force the use of GnuPG-1 on
systems which have both GPG versions installed.  Note that in general
the use of @code{gpgme_set_engine_info} is a better way to select a
specific engine version.

@item gpgconf-name
@itemx gpg-name
Set the name of the gpgconf respective gpg binary.  The defaults are
@code{GNU/GnuPG/gpgconf} and @code{GNU/GnuPG/gpg}.  Under Unix the
leading directory part is ignored.  Under Windows the leading
directory part is used as the default installation directory; the
@code{.exe} suffix is added by GPGME.  Use forward slashed even under
Windows.

@item require-gnupg
Set the minimum version of the required GnuPG engine.  If that version
is not met, GPGME fails early instead of trying to use the existent
version.  The given version must be a string with major, minor, and
micro number.  Example: "2.1.0".

@item inst-type
The installation type is used to prefer a certain GnuPG installation.
The value is interpreted as an integer: A value of 0 is ignored, a
value of 1 indicates an installation scheme as used by Gpg4win, a
value of 2 indicates an installation scheme as used by GnuPG Desktop
on Windows.  All other values are reserved.

@item w32-inst-dir
On Windows GPGME needs to know its installation directory to find its
spawn helper.  This is in general no problem because a DLL has this
information.  Some applications however link statically to GPGME and
thus GPGME can only figure out the installation directory of this
application which may be wrong in certain cases.  By supplying an
installation directory as value to this flag, GPGME will assume that
that directory is the installation directory.  This flag has no effect
on non-Windows platforms.

@end table

This function returns @code{0} on success.  In contrast to other
functions the non-zero return value on failure does not convey any
error code.  For setting ``debug'' the only possible error cause is an
out of memory condition; which would exhibit itself later anyway.
Thus the return value may be ignored.
@end deftypefun


After initializing @acronym{GPGME}, you should set the locale
information to the locale required for your output terminal.  This
locale information is needed for example for the curses and Gtk
pinentry.  Here is an example of a complete initialization:

@example
#include <locale.h>
#include <gpgme.h>

void
init_gpgme (void)
@{
  /* Initialize the locale environment.  */
  setlocale (LC_ALL, "");
  gpgme_check_version (NULL);
  gpgme_set_locale (NULL, LC_CTYPE, setlocale (LC_CTYPE, NULL));
#ifdef LC_MESSAGES
  gpgme_set_locale (NULL, LC_MESSAGES, setlocale (LC_MESSAGES, NULL));
#endif
@}
@end example

Note that you are highly recommended to initialize the locale settings
like this.  @acronym{GPGME} can not do this for you because it would
not be thread safe.  The conditional on LC_MESSAGES is only necessary
for portability to W32 systems.


@node Signal Handling
@section Signal Handling
@cindex signals
@cindex signal handling

The @acronym{GPGME} library communicates with child processes (the
crypto engines).  If a child process dies unexpectedly, for example
due to a bug, or system problem, a @code{SIGPIPE} signal will be
delivered to the application.  The default action is to abort the
program.  To protect against this, @code{gpgme_check_version} sets the
@code{SIGPIPE} signal action to @code{SIG_IGN}, which means that the
signal will be ignored.

@acronym{GPGME} will only do that if the signal action for
@code{SIGPIPE} is @code{SIG_DEF} at the time
@code{gpgme_check_version} is called.  If it is something different,
@code{GPGME} will take no action.

This means that if your application does not install any signal
handler for @code{SIGPIPE}, you don't need to take any precautions.
If you do install a signal handler for @code{SIGPIPE}, you must be
prepared to handle any @code{SIGPIPE} events that occur due to
@acronym{GPGME} writing to a defunct pipe.  Furthermore, if your
application is multi-threaded, and you install a signal action for
@code{SIGPIPE}, you must make sure you do this either before
@code{gpgme_check_version} is called or afterwards.


@node Multi-Threading
@section Multi-Threading
@cindex thread-safeness
@cindex multi-threading

The @acronym{GPGME} library is mostly thread-safe, and can be used
in a multi-threaded environment but there are some requirements
for multi-threaded use:

@itemize @bullet
@item
The function @code{gpgme_check_version} must be called before any
other function in the library, because it initializes the thread
support subsystem in @acronym{GPGME}.  To achieve this in
multi-threaded programs, you must synchronize the memory with respect
to other threads that also want to use @acronym{GPGME}.  For this, it
is sufficient to call @code{gpgme_check_version} before creating the
other threads using @acronym{GPGME}@footnote{At least this is true for
POSIX threads, as @code{pthread_create} is a function that
synchronizes memory with respects to other threads.  There are many
functions which have this property, a complete list can be found in
POSIX, IEEE Std 1003.1-2003, Base Definitions, Issue 6, in the
definition of the term ``Memory Synchronization''.  For other thread
packages other, more relaxed or more strict rules may apply.}.

@item
Any @code{gpgme_data_t} and @code{gpgme_ctx_t} object must only be
accessed by one thread at a time.  If multiple threads want to deal
with the same object, the caller has to make sure that operations on
that object are fully synchronized.

@item
Only one thread at any time is allowed to call @code{gpgme_wait}.  If
multiple threads call this function, the caller must make sure that
all invocations are fully synchronized.  It is safe to start
asynchronous operations while a thread is running in gpgme_wait.

@item
The function @code{gpgme_strerror} is not thread safe.  You have to
use @code{gpgme_strerror_r} instead.
@end itemize


@node Protocols and Engines
@chapter Protocols and Engines
@cindex protocol
@cindex engine
@cindex crypto engine
@cindex backend
@cindex crypto backend

@acronym{GPGME} supports several cryptographic protocols, however, it
does not implement them.  Rather it uses backends (also called
engines) which implement the protocol.  @acronym{GPGME} uses
inter-process communication to pass data back and forth between the
application and the backend, but the details of the communication
protocol and invocation of the backend is completely hidden by the
interface.  All complexity is handled by @acronym{GPGME}.  Where an
exchange of information between the application and the backend is
necessary, @acronym{GPGME} provides the necessary callback function
hooks and further interfaces.

@deftp {Data type} {enum gpgme_protocol_t}
@tindex gpgme_protocol_t
The @code{gpgme_protocol_t} type specifies the set of possible protocol
values that are supported by @acronym{GPGME}.  The following protocols
are supported:

@table @code
@item GPGME_PROTOCOL_OpenPGP
@itemx GPGME_PROTOCOL_OPENPGP
This specifies the OpenPGP protocol.

@item GPGME_PROTOCOL_CMS
This specifies the Cryptographic Message Syntax.

@item GPGME_PROTOCOL_GPGCONF
Under development.  Please ask on @email{gnupg-devel@@gnupg.org} for help.

@item GPGME_PROTOCOL_ASSUAN
@since{1.2.0}

This specifies the raw Assuan protocol.

@item GPGME_PROTOCOL_G13
@since{1.3.0}

Under development.  Please ask on @email{gnupg-devel@@gnupg.org} for help.

@item GPGME_PROTOCOL_UISERVER
Under development.  Please ask on @email{gnupg-devel@@gnupg.org} for help.

@item GPGME_PROTOCOL_SPAWN
@since{1.5.0}

Special protocol for use with @code{gpgme_op_spawn}.

@item GPGME_PROTOCOL_UNKNOWN
Reserved for future extension.  You may use this to indicate that the
used protocol is not known to the application.  Currently,
@acronym{GPGME} does not accept this value in any operation, though,
except for @code{gpgme_get_protocol_name}.
@end table
@end deftp


@deftypefun {const char *} gpgme_get_protocol_name (@w{gpgme_protocol_t @var{protocol}})
The function @code{gpgme_get_protocol_name} returns a statically
allocated string describing the protocol @var{protocol}, or
@code{NULL} if the protocol number is not valid.
@end deftypefun

@menu
* Engine Version Check::          Verifying the engine version.
* Engine Information::            Obtaining more information about the engines.
* Engine Configuration::          Changing the engine configuration.
* OpenPGP::                       Support for the OpenPGP protocol.
* Cryptographic Message Syntax::  Support for the CMS.
* Assuan::                        Support for the raw Assuan protocol.
@end menu


@node Engine Version Check
@section Engine Version Check
@cindex version check, of the engines

@deftypefun @w{const char *} gpgme_get_dirinfo (@w{cons char *@var{what}})
@since{1.5.0}

The function @code{gpgme_get_dirinfo} returns a statically allocated
string with the value associated to @var{what}.  The returned values
are the defaults and won't change even after
@code{gpgme_set_engine_info} has been used to configure a different
engine.  @code{NULL} is returned if no value is available.  Commonly
supported values for @var{what} are:

@table @code
@item homedir
Return the default home directory.

@item sysconfdir
Return the name of the system configuration directory

@item bindir
Return the name of the directory with GnuPG program files.

@item libdir
Return the name of the directory with GnuPG related library files.

@item libexecdir
Return the name of the directory with GnuPG helper program files.

@item datadir
Return the name of the directory with GnuPG shared data.

@item localedir
Return the name of the directory with GnuPG locale data.

@item socketdir
Return the name of the directory with the following sockets.

@item agent-socket
Return the name of the socket to connect to the gpg-agent.

@item agent-ssh-socket
Return the name of the socket to connect to the ssh-agent component of
gpg-agent.

@item dirmngr-socket
Return the name of the socket to connect to the dirmngr.

@item uiserver-socket
Return the name of the socket to connect to the user interface server.

@item gpgconf-name
Return the file name of the engine configuration tool.

@item gpg-name
Return the file name of the OpenPGP engine.

@item gpgsm-name
Return the file name of the CMS engine.

@item g13-name
Return the name of the file container encryption engine.

@item keyboxd-name
Return the name of the key database daemon.

@item agent-name
Return the name of gpg-agent.

@item scdaemon-name
Return the name of the smart card daemon.

@item dirmngr-name
Return the name of dirmngr.

@item pinentry-name
Return the name of the pinentry program.

@item gpg-wks-client-name
Return the name of the Web Key Service tool.

@item gpgtar-name
Return the name of the gpgtar program.

@end table

@end deftypefun


@deftypefun gpgme_error_t gpgme_engine_check_version (@w{gpgme_protocol_t @var{protocol}})
The function @code{gpgme_engine_check_version} verifies that the
engine implementing the protocol @var{PROTOCOL} is installed in the
expected path and meets the version requirement of @acronym{GPGME}.

This function returns the error code @code{GPG_ERR_NO_ERROR} if the
engine is available and @code{GPG_ERR_INV_ENGINE} if it is not.
@end deftypefun


@node Engine Information
@section Engine Information
@cindex engine, information about

@deftp {Data type} {gpgme_engine_info_t}
@tindex gpgme_protocol_t
The @code{gpgme_engine_info_t} type specifies a pointer to a structure
describing a crypto engine.  The structure contains the following
elements:

@table @code
@item gpgme_engine_info_t next
This is a pointer to the next engine info structure in the linked
list, or @code{NULL} if this is the last element.

@item gpgme_protocol_t protocol
This is the protocol for which the crypto engine is used.  You can
convert this to a string with @code{gpgme_get_protocol_name} for
printing.

@item const char *file_name
This is a string holding the file name of the executable of the crypto
engine.  Currently, it is never @code{NULL}, but using @code{NULL} is
reserved for future use, so always check before you use it.

@item const char *home_dir
This is a string holding the directory name of the crypto engine's
configuration directory.  If it is @code{NULL}, then the default
directory is used.  See @code{gpgme_get_dirinfo} on how to get the
default directory.

@item const char *version
This is a string containing the version number of the crypto engine.
It might be @code{NULL} if the version number can not be determined,
for example because the executable doesn't exist or is invalid.

@item const char *req_version
This is a string containing the minimum required version number of the
crypto engine for @acronym{GPGME} to work correctly.  This is the
version number that @code{gpgme_engine_check_version} verifies
against.  Currently, it is never @code{NULL}, but using @code{NULL} is
reserved for future use, so always check before you use it.
@end table
@end deftp

@deftypefun gpgme_error_t gpgme_get_engine_info (@w{gpgme_engine_info_t *@var{info}})
The function @code{gpgme_get_engine_info} returns a linked list of
engine info structures in @var{info}.  Each info structure describes
the defaults of one configured backend.

The memory for the info structures is allocated the first time this
function is invoked, and must not be freed by the caller.

This function returns the error code @code{GPG_ERR_NO_ERROR} if
successful, and a system error if the memory could not be allocated.
@end deftypefun

Here is an example how you can provide more diagnostics if you receive
an error message which indicates that the crypto engine is invalid.

@example
gpgme_ctx_t ctx;
gpgme_error_t err;

[...]

if (gpgme_err_code (err) == GPG_ERR_INV_ENGINE)
  @{
    gpgme_engine_info_t info;
    err = gpgme_get_engine_info (&info);
    if (!err)
      @{
        while (info && info->protocol != gpgme_get_protocol (ctx))
          info = info->next;
        if (!info)
          fprintf (stderr, "GPGME compiled without support for protocol %s",
                   gpgme_get_protocol_name (info->protocol));
        else if (info->file_name && !info->version)
          fprintf (stderr, "Engine %s not installed properly",
                   info->file_name);
        else if (info->file_name && info->version && info->req_version)
          fprintf (stderr, "Engine %s version %s installed, "
                   "but at least version %s required", info->file_name,
                   info->version, info->req_version);
        else
          fprintf (stderr, "Unknown problem with engine for protocol %s",
                   gpgme_get_protocol_name (info->protocol));
      @}
  @}
@end example


@node Engine Configuration
@section Engine Configuration
@cindex engine, configuration of
@cindex configuration of crypto backend

You can change the configuration of a backend engine, and thus change
the executable program and configuration directory to be used.  You
can make these changes the default or set them for some contexts
individually.

@deftypefun gpgme_error_t gpgme_set_engine_info (@w{gpgme_protocol_t @var{proto}}, @w{const char *@var{file_name}}, @w{const char *@var{home_dir}})
@since{1.1.0}

The function @code{gpgme_set_engine_info} changes the default
configuration of the crypto engine implementing the protocol
@var{proto}.

@var{file_name} is the file name of the executable program
implementing this protocol, and @var{home_dir} is the directory name
of the configuration directory for this crypto engine.  If
@var{home_dir} is @code{NULL}, the engine's default will be used.

The new defaults are not applied to already created GPGME contexts.

This function returns the error code @code{GPG_ERR_NO_ERROR} if
successful, or an error code on failure.
@end deftypefun

The functions @code{gpgme_ctx_get_engine_info} and
@code{gpgme_ctx_set_engine_info} can be used to change the engine
configuration per context.  @xref{Crypto Engine}.


@node OpenPGP
@section OpenPGP
@cindex OpenPGP
@cindex GnuPG
@cindex protocol, GnuPG
@cindex engine, GnuPG

OpenPGP is implemented by GnuPG, the @acronym{GNU} Privacy Guard.
This is the first protocol that was supported by @acronym{GPGME}.

The OpenPGP protocol is specified by @code{GPGME_PROTOCOL_OpenPGP}.


@node Cryptographic Message Syntax
@section Cryptographic Message Syntax
@cindex CMS
@cindex cryptographic message syntax
@cindex GpgSM
@cindex protocol, CMS
@cindex engine, GpgSM
@cindex S/MIME
@cindex protocol, S/MIME

@acronym{CMS} is implemented by GpgSM, the S/MIME implementation for
GnuPG.

The @acronym{CMS} protocol is specified by @code{GPGME_PROTOCOL_CMS}.


@node Assuan
@section Assuan
@cindex ASSUAN
@cindex protocol, ASSUAN
@cindex engine, ASSUAN

Assuan is the RPC library used by the various @acronym{GnuPG}
components.  The Assuan protocol allows one to talk to arbitrary
Assuan servers using @acronym{GPGME}.  @xref{Using the Assuan
protocol}.

The ASSUAN protocol is specified by @code{GPGME_PROTOCOL_ASSUAN}.


@node Algorithms
@chapter Algorithms
@cindex algorithms

The crypto backends support a variety of algorithms used in public key
cryptography.@footnote{Some engines also provide symmetric only
encryption; see the description of the encryption function on how to use
this.}  The following sections list the identifiers used to denote such
an algorithm.

@menu
* Public Key Algorithms::         A list of all public key algorithms.
* Hash Algorithms::               A list of all hash algorithms.
@end menu


@node Public Key Algorithms
@section Public Key Algorithms
@cindex algorithms, public key
@cindex public key algorithms

Public key algorithms are used for encryption, decryption, signing and
verification of signatures.

@deftp {Data type} {enum gpgme_pubkey_algo_t}
@tindex gpgme_pubkey_algo_t
The @code{gpgme_pubkey_algo_t} type specifies the set of all public key
algorithms that are supported by @acronym{GPGME}.  Possible values
are:

@table @code
@item GPGME_PK_RSA
This value indicates the RSA (Rivest, Shamir, Adleman) algorithm.

@item GPGME_PK_RSA_E
Deprecated.  This value indicates the RSA (Rivest, Shamir, Adleman)
algorithm for encryption and decryption only.

@item GPGME_PK_RSA_S
Deprecated.  This value indicates the RSA (Rivest, Shamir, Adleman)
algorithm for signing and verification only.

@item GPGME_PK_DSA
This value indicates DSA, the Digital Signature Algorithm.

@item GPGME_PK_ELG
This value indicates ElGamal.

@item GPGME_PK_ELG_E
This value also indicates ElGamal and is used specifically in GnuPG.

@item GPGME_PK_ECC
@since{1.5.0}

This value is a generic indicator for ellipic curve algorithms.

@item GPGME_PK_ECDSA
@since{1.3.0}

This value indicates ECDSA, the Elliptic Curve Digital Signature
Algorithm as defined by FIPS 186-2 and RFC-6637.

@item GPGME_PK_ECDH
@since{1.3.0}

This value indicates ECDH, the Eliptic Curve Diffie-Hellmann
encryption algorithm as defined by RFC-6637.

@item GPGME_PK_EDDSA
@since{1.7.0}

This value indicates the EdDSA algorithm.

@end table
@end deftp

@deftypefun {const char *} gpgme_pubkey_algo_name (@w{gpgme_pubkey_algo_t @var{algo}})
The function @code{gpgme_pubkey_algo_name} returns a pointer to a
statically allocated string containing a description of the public key
algorithm @var{algo}.  This string can be used to output the name of
the public key algorithm to the user.

If @var{algo} is not a valid public key algorithm, @code{NULL} is
returned.
@end deftypefun

@deftypefun {char *} gpgme_pubkey_algo_string (@w{gpgme_subkey_t @var{key}})
@since{1.7.0}

The function @code{gpgme_pubkey_algo_string} is a convenience function
to build and return an algorithm string in the same way GnuPG does
(e.g., ``rsa2048'' or ``ed25519'').  The caller must free the result
using @code{gpgme_free}.  On error (e.g., invalid argument or memory
exhausted), the function returns NULL and sets @code{ERRNO}.
@end deftypefun


@node Hash Algorithms
@section Hash Algorithms
@cindex algorithms, hash
@cindex algorithms, message digest
@cindex hash algorithms
@cindex message digest algorithms

Hash (message digest) algorithms are used to compress a long message
to make it suitable for public key cryptography.

@deftp {Data type} {enum gpgme_hash_algo_t}
@tindex gpgme_hash_algo_t
The @code{gpgme_hash_algo_t} type specifies the set of all hash algorithms
that are supported by @acronym{GPGME}.  Possible values are:

@table @code
@item GPGME_MD_MD5
@item GPGME_MD_SHA1
@item GPGME_MD_RMD160
@item GPGME_MD_MD2
@item GPGME_MD_TIGER
@item GPGME_MD_HAVAL
@item GPGME_MD_SHA256
@item GPGME_MD_SHA384
@item GPGME_MD_SHA512
@item GPGME_MD_SHA224
@since{1.5.0}

@item GPGME_MD_MD4
@item GPGME_MD_CRC32
@item GPGME_MD_CRC32_RFC1510
@item GPGME_MD_CRC24_RFC2440
@end table
@end deftp

@deftypefun {const char *} gpgme_hash_algo_name (@w{gpgme_hash_algo_t @var{algo}})
The function @code{gpgme_hash_algo_name} returns a pointer to a
statically allocated string containing a description of the hash
algorithm @var{algo}.  This string can be used to output the name of
the hash algorithm to the user.

If @var{algo} is not a valid hash algorithm, @code{NULL} is returned.
@end deftypefun


@node Error Handling
@chapter Error Handling
@cindex error handling

Many functions in @acronym{GPGME} can return an error if they fail.
For this reason, the application should always catch the error
condition and take appropriate measures, for example by releasing the
resources and passing the error up to the caller, or by displaying a
descriptive message to the user and cancelling the operation.

Some error values do not indicate a system error or an error in the
operation, but the result of an operation that failed properly.  For
example, if you try to decrypt a tempered message, the decryption will
fail.  Another error value actually means that the end of a data
buffer or list has been reached.  The following descriptions explain
for many error codes what they mean usually.  Some error values have
specific meanings if returned by a certain functions.  Such cases are
described in the documentation of those functions.

@acronym{GPGME} uses the @code{libgpg-error} library.  This allows to
share the error codes with other components of the GnuPG system, and
thus pass error values transparently from the crypto engine, or some
helper application of the crypto engine, to the user.  This way no
information is lost.  As a consequence, @acronym{GPGME} does not use
its own identifiers for error codes, but uses those provided by
@code{libgpg-error}.  They usually start with @code{GPG_ERR_}.

However, @acronym{GPGME} does provide aliases for the functions
defined in libgpg-error, which might be preferred for name space
consistency.

@menu
* Error Values::                  The error value and what it means.
* Error Sources::                 A list of important error sources.
* Error Codes::                   A list of important error codes.
* Error Strings::                 How to get a descriptive string from a value.
@end menu


@node Error Values
@section Error Values
@cindex error values
@cindex error codes
@cindex error sources

@deftp {Data type} {gpgme_err_code_t}
The @code{gpgme_err_code_t} type is an alias for the @code{libgpg-error}
type @code{gpg_err_code_t}.  The error code indicates the type of an
error, or the reason why an operation failed.

A list of important error codes can be found in the next section.
@end deftp

@deftp {Data type} {gpgme_err_source_t}
The @code{gpgme_err_source_t} type is an alias for the
@code{libgpg-error} type @code{gpg_err_source_t}.  The error source
has not a precisely defined meaning.  Sometimes it is the place where
the error happened, sometimes it is the place where an error was
encoded into an error value.  Usually the error source will give an
indication to where to look for the problem.  This is not always true,
but it is attempted to achieve this goal.

A list of important error sources can be found in the next section.
@end deftp

@deftp {Data type} {gpgme_error_t}
The @code{gpgme_error_t} type is an alias for the @code{libgpg-error}
type @code{gpg_error_t}.  An error value like this has always two
components, an error code and an error source.  Both together form the
error value.

Thus, the error value can not be directly compared against an error
code, but the accessor functions described below must be used.
However, it is guaranteed that only 0 is used to indicate success
(@code{GPG_ERR_NO_ERROR}), and that in this case all other parts of
the error value are set to 0, too.

Note that in @acronym{GPGME}, the error source is used purely for
diagnostical purposes.  Only the error code should be checked to test
for a certain outcome of a function.  The manual only documents the
error code part of an error value.  The error source is left
unspecified and might be anything.
@end deftp

@deftypefun {static inline gpgme_err_code_t} gpgme_err_code (@w{gpgme_error_t @var{err}})
The static inline function @code{gpgme_err_code} returns the
@code{gpgme_err_code_t} component of the error value @var{err}.  This
function must be used to extract the error code from an error value in
order to compare it with the @code{GPG_ERR_*} error code macros.
@end deftypefun

@deftypefun {static inline gpgme_err_source_t} gpgme_err_source (@w{gpgme_error_t @var{err}})
The static inline function @code{gpgme_err_source} returns the
@code{gpgme_err_source_t} component of the error value @var{err}.  This
function must be used to extract the error source from an error value in
order to compare it with the @code{GPG_ERR_SOURCE_*} error source macros.
@end deftypefun

@deftypefun {static inline gpgme_error_t} gpgme_err_make (@w{gpgme_err_source_t @var{source}}, @w{gpgme_err_code_t @var{code}})
The static inline function @code{gpgme_err_make} returns the error
value consisting of the error source @var{source} and the error code
@var{code}.

This function can be used in callback functions to construct an error
value to return it to the library.
@end deftypefun

@deftypefun {static inline gpgme_error_t} gpgme_error (@w{gpgme_err_code_t @var{code}})
The static inline function @code{gpgme_error} returns the error value
consisting of the default error source and the error code @var{code}.

For @acronym{GPGME} applications, the default error source is
@code{GPG_ERR_SOURCE_USER_1}.  You can define
@code{GPGME_ERR_SOURCE_DEFAULT} before including @file{gpgme.h} to
change this default.

This function can be used in callback functions to construct an error
value to return it to the library.
@end deftypefun

The @code{libgpg-error} library provides error codes for all system
error numbers it knows about.  If @var{err} is an unknown error
number, the error code @code{GPG_ERR_UNKNOWN_ERRNO} is used.  The
following functions can be used to construct error values from system
errnor numbers.

@deftypefun {gpgme_error_t} gpgme_err_make_from_errno (@w{gpgme_err_source_t @var{source}}, @w{int @var{err}})
The function @code{gpgme_err_make_from_errno} is like
@code{gpgme_err_make}, but it takes a system error like @code{errno}
instead of a @code{gpgme_err_code_t} error code.
@end deftypefun

@deftypefun {gpgme_error_t} gpgme_error_from_errno (@w{int @var{err}})
The function @code{gpgme_error_from_errno} is like @code{gpgme_error},
but it takes a system error like @code{errno} instead of a
@code{gpgme_err_code_t} error code.
@end deftypefun

Sometimes you might want to map system error numbers to error codes
directly, or map an error code representing a system error back to the
system error number.  The following functions can be used to do that.

@deftypefun {gpgme_err_code_t} gpgme_err_code_from_errno (@w{int @var{err}})
The function @code{gpgme_err_code_from_errno} returns the error code
for the system error @var{err}.  If @var{err} is not a known system
error, the function returns @code{GPG_ERR_UNKNOWN_ERRNO}.
@end deftypefun

@deftypefun {int} gpgme_err_code_to_errno (@w{gpgme_err_code_t @var{err}})
The function @code{gpgme_err_code_to_errno} returns the system error
for the error code @var{err}.  If @var{err} is not an error code
representing a system error, or if this system error is not defined on
this system, the function returns @code{0}.
@end deftypefun


@node Error Sources
@section Error Sources
@cindex error codes, list of

The library @code{libgpg-error} defines an error source for every
component of the GnuPG system.  The error source part of an error
value is not well defined.  As such it is mainly useful to improve the
diagnostic error message for the user.

If the error code part of an error value is @code{0}, the whole error
value will be @code{0}.  In this case the error source part is of
course @code{GPG_ERR_SOURCE_UNKNOWN}.

The list of error sources that might occur in applications using
@acronym{GPGME} is:

@table @code
@item GPG_ERR_SOURCE_UNKNOWN
The error source is not known.  The value of this error source is
@code{0}.

@item GPG_ERR_SOURCE_GPGME
The error source is @acronym{GPGME} itself.  This is the default for
errors that occur in the @acronym{GPGME} library.

@item GPG_ERR_SOURCE_GPG
The error source is GnuPG, which is the crypto engine used for the
OpenPGP protocol.

@item GPG_ERR_SOURCE_GPGSM
The error source is GPGSM, which is the crypto engine used for the
CMS protocol.

@item GPG_ERR_SOURCE_GCRYPT
The error source is @code{libgcrypt}, which is used by crypto engines
to perform cryptographic operations.

@item GPG_ERR_SOURCE_GPGAGENT
The error source is @command{gpg-agent}, which is used by crypto
engines to perform operations with the secret key.

@item GPG_ERR_SOURCE_PINENTRY
The error source is @command{pinentry}, which is used by
@command{gpg-agent} to query the passphrase to unlock a secret key.

@item GPG_ERR_SOURCE_SCD
The error source is the SmartCard Daemon, which is used by
@command{gpg-agent} to delegate operations with the secret key to a
SmartCard.

@item GPG_ERR_SOURCE_KEYBOX
The error source is @code{libkbx}, a library used by the crypto
engines to manage local keyrings.

@item GPG_ERR_SOURCE_USER_1
@item GPG_ERR_SOURCE_USER_2
@item GPG_ERR_SOURCE_USER_3
@item GPG_ERR_SOURCE_USER_4
These error sources are not used by any GnuPG component and can be
used by other software.  For example, applications using
@acronym{GPGME} can use them to mark error values coming from callback
handlers.  Thus @code{GPG_ERR_SOURCE_USER_1} is the default for errors
created with @code{gpgme_error} and @code{gpgme_error_from_errno},
unless you define @code{GPGME_ERR_SOURCE_DEFAULT} before including
@file{gpgme.h}.
@end table


@node Error Codes
@section Error Codes
@cindex error codes, list of

The library @code{libgpg-error} defines many error values.  Most of
them are not used by @code{GPGME} directly, but might be returned by
@acronym{GPGME} because it received them from the crypto engine.  The
below list only includes such error codes that have a specific meaning
in @code{GPGME}, or which are so common that you should know about
them.

@table @code
@item GPG_ERR_EOF
This value indicates the end of a list, buffer or file.

@item GPG_ERR_NO_ERROR
This value indicates success.  The value of this error code is
@code{0}.  Also, it is guaranteed that an error value made from the
error code @code{0} will be @code{0} itself (as a whole).  This means
that the error source information is lost for this error code,
however, as this error code indicates that no error occurred, this is
generally not a problem.

@item GPG_ERR_GENERAL
This value means that something went wrong, but either there is not
enough information about the problem to return a more useful error
value, or there is no separate error value for this type of problem.

@item GPG_ERR_ENOMEM
This value means that an out-of-memory condition occurred.

@item GPG_ERR_E...
System errors are mapped to GPG_ERR_FOO where FOO is the symbol for
the system error.

@item GPG_ERR_INV_VALUE
This value means that some user provided data was out of range.  This
can also refer to objects.  For example, if an empty
@code{gpgme_data_t} object was expected, but one containing data was
provided, this error value is returned.

@item GPG_ERR_UNUSABLE_PUBKEY
This value means that some recipients for a message were invalid.

@item GPG_ERR_UNUSABLE_SECKEY
This value means that some signers were invalid.

@item GPG_ERR_NO_DATA
This value means that a @code{gpgme_data_t} object which was expected
to have content was found empty.

@item GPG_ERR_CONFLICT
This value means that a conflict of some sort occurred.

@item GPG_ERR_NOT_IMPLEMENTED
This value indicates that the specific function (or operation) is not
implemented.  This error should never happen.  It can only occur if
you use certain values or configuration options which do not work,
but for which we think that they should work at some later time.

@item GPG_ERR_DECRYPT_FAILED
This value indicates that a decryption operation was unsuccessful.

@item GPG_ERR_BAD_PASSPHRASE
This value means that the user did not provide a correct passphrase
when requested.

@item GPG_ERR_CANCELED
This value means that the operation was canceled.

@item GPG_ERR_FULLY_CANCELED
This value means that the operation was canceled. It is sometimes returned
instead of @code{GPG_ERR_CANCELED} for internal reasons in GnuPG. You should
treat both values identically.

@item GPG_ERR_INV_ENGINE
This value means that the engine that implements the desired protocol
is currently not available.  This can either be because the sources
were configured to exclude support for this engine, or because the
engine is not installed properly.

@item GPG_ERR_AMBIGUOUS_NAME
This value indicates that a user ID or other specifier did not specify
a unique key.

@item GPG_ERR_WRONG_KEY_USAGE
This value indicates that a key is not used appropriately.

@item GPG_ERR_CERT_REVOKED
This value indicates that a key signature was revoced.

@item GPG_ERR_CERT_EXPIRED
This value indicates that a key signature expired.

@item GPG_ERR_NO_CRL_KNOWN
This value indicates that no certificate revocation list is known for
the certificate.

@item GPG_ERR_NO_POLICY_MATCH
This value indicates that a policy issue occurred.

@item GPG_ERR_NO_SECKEY
This value indicates that no secret key for the user ID is available.

@item GPG_ERR_MISSING_CERT
This value indicates that a key could not be imported because the
issuer certificate is missing.

@item GPG_ERR_BAD_CERT_CHAIN
This value indicates that a key could not be imported because its
certificate chain is not good, for example it could be too long.

@item GPG_ERR_UNSUPPORTED_ALGORITHM
This value means a verification failed because the cryptographic
algorithm is not supported by the crypto backend.

@item GPG_ERR_BAD_SIGNATURE
This value means a verification failed because the signature is bad.

@item GPG_ERR_NO_PUBKEY
This value means a verification failed because the public key is not
available.

@item GPG_ERR_USER_1
@item GPG_ERR_USER_2
@item ...
@item GPG_ERR_USER_16
These error codes are not used by any GnuPG component and can be
freely used by other software.  Applications using @acronym{GPGME}
might use them to mark specific errors returned by callback handlers
if no suitable error codes (including the system errors) for
these errors exist already.
@end table


@node Error Strings
@section Error Strings
@cindex error values, printing of
@cindex error codes, printing of
@cindex error sources, printing of
@cindex error strings

@deftypefun {const char *} gpgme_strerror (@w{gpgme_error_t @var{err}})
The function @code{gpgme_strerror} returns a pointer to a statically
allocated string containing a description of the error code contained
in the error value @var{err}.  This string can be used to output a
diagnostic message to the user.

This function is not thread safe.  Use @code{gpgme_strerror_r} in
multi-threaded programs.
@end deftypefun


@deftypefun {int} gpgme_strerror_r (@w{gpgme_error_t @var{err}}, @w{char *@var{buf}}, @w{size_t @var{buflen}})
The function @code{gpgme_strerror_r} returns the error string for
@var{err} in the user-supplied buffer @var{buf} of size @var{buflen}.
This function is, in contrast to @code{gpgme_strerror}, thread-safe if
a thread-safe @code{strerror_r} function is provided by the system.
If the function succeeds, 0 is returned and @var{buf} contains the
string describing the error.  If the buffer was not large enough,
ERANGE is returned and @var{buf} contains as much of the beginning of
the error string as fits into the buffer.
@end deftypefun


@deftypefun {const char *} gpgme_strsource (@w{gpgme_error_t @var{err}})
The function @code{gpgme_strerror} returns a pointer to a statically
allocated string containing a description of the error source
contained in the error value @var{err}.  This string can be used to
output a diagnostic message to the user.
@end deftypefun

The following example illustrates the use of @code{gpgme_strerror}:

@example
gpgme_ctx_t ctx;
gpgme_error_t err = gpgme_new (&ctx);
if (err)
  @{
    fprintf (stderr, "%s: creating GpgME context failed: %s: %s\n",
             argv[0], gpgme_strsource (err), gpgme_strerror (err));
    exit (1);
  @}
@end example


@node Exchanging Data
@chapter Exchanging Data
@cindex data, exchanging

A lot of data has to be exchanged between the user and the crypto
engine, like plaintext messages, ciphertext, signatures and
information about the keys.  The technical details about exchanging
the data information are completely abstracted by @acronym{GPGME}.
The user provides and receives the data via @code{gpgme_data_t} objects,
regardless of the communication protocol between @acronym{GPGME} and
the crypto engine in use.

@deftp {Data type} {gpgme_data_t}
The @code{gpgme_data_t} type is a handle for a container for generic
data, which is used by @acronym{GPGME} to exchange data with the user.
@end deftp

@code{gpgme_data_t} objects do not provide notifications on events.
It is assumed that read and write operations are blocking until data
is available.  If this is undesirable, the application must ensure
that all GPGME data operations always have data available, for example
by using memory buffers or files rather than pipes or sockets.  This
might be relevant, for example, if the external event loop mechanism
is used.

@deftp {Data type} {gpgme_off_t}
@since{1.4.1}

On POSIX platforms the @code{gpgme_off_t} type is an alias for
@code{off_t}; it may be used interchangeable.  On Windows platforms
@code{gpgme_off_t} is defined as a long (i.e., 32 bit) for 32 bit
Windows and as a 64 bit signed integer for 64 bit Windows.
@end deftp

@deftp {Data type} {gpgme_ssize_t}
The @code{gpgme_ssize_t} type is an alias for @code{ssize_t}.  It has
only been introduced to overcome portability problems pertaining to
the declaration of @code{ssize_t} by different toolchains.
@end deftp


@menu
* Creating Data Buffers::         Creating new data buffers.
* Destroying Data Buffers::       Releasing data buffers.
* Manipulating Data Buffers::     Operations on data buffers.
@end menu


@node Creating Data Buffers
@section Creating Data Buffers
@cindex data buffer, creation

Data objects can be based on memory, files, or callback functions
provided by the user.  Not all operations are supported by all
objects.


@menu
* Memory Based Data Buffers::     Creating memory based data buffers.
* File Based Data Buffers::       Creating file based data buffers.
* Callback Based Data Buffers::   Creating callback based data buffers.
@end menu


@node Memory Based Data Buffers
@subsection Memory Based Data Buffers

Memory based data objects store all data in allocated memory.  This is
convenient, but only practical for an amount of data that is a
fraction of the available physical memory.  The data has to be copied
from its source and to its destination, which can often be avoided by
using one of the other data object

@deftypefun gpgme_error_t gpgme_data_new (@w{gpgme_data_t *@var{dh}})
The function @code{gpgme_data_new} creates a new @code{gpgme_data_t}
object and returns a handle for it in @var{dh}.  The data object is
memory based and initially empty.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{dh} is not a valid pointer, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_new_from_mem (@w{gpgme_data_t *@var{dh}}, @w{const char *@var{buffer}}, @w{size_t @var{size}}, @w{int @var{copy}})
The function @code{gpgme_data_new_from_mem} creates a new
@code{gpgme_data_t} object and fills it with @var{size} bytes starting
from @var{buffer}.

If @var{copy} is not zero, a private copy of the data is made.  If
@var{copy} is zero, the data is taken from the specified buffer as
needed, and the user has to ensure that the buffer remains valid for
the whole life span of the data object.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{dh} or @var{buffer} is not a valid pointer, and
@code{GPG_ERR_ENOMEM} if not enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_new_from_file (@w{gpgme_data_t *@var{dh}}, @w{const char *@var{filename}}, @w{int @var{copy}})
The function @code{gpgme_data_new_from_file} creates a new
@code{gpgme_data_t} object and fills it with the content of the file
@var{filename}.

If @var{copy} is not zero, the whole file is read in at initialization
time and the file is not used anymore after that.  This is the only
mode supported currently.  Later, a value of zero for @var{copy} might
cause all reads to be delayed until the data is needed, but this is
not yet implemented.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{dh} or @var{filename} is not a valid pointer,
@code{GPG_ERR_NOT_IMPLEMENTED} if @var{code} is zero, and
@code{GPG_ERR_ENOMEM} if not enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_new_from_filepart (@w{gpgme_data_t *@var{dh}}, @w{const char *@var{filename}}, @w{FILE *@var{fp}}, @w{off_t @var{offset}}, @w{size_t @var{length}})
The function @code{gpgme_data_new_from_filepart} creates a new
@code{gpgme_data_t} object and fills it with a part of the file specified
by @var{filename} or @var{fp}.

Exactly one of @var{filename} and @var{fp} must be non-zero, the other
must be zero.  The argument that is not zero specifies the file from
which @var{length} bytes are read into the data object, starting from
@var{offset}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{dh} and exactly one of @var{filename} and @var{fp} is not a valid
pointer, and @code{GPG_ERR_ENOMEM} if not enough memory is available.
@end deftypefun


@node File Based Data Buffers
@subsection File Based Data Buffers

File based data objects operate directly on file descriptors or
streams.  Only a small amount of data is stored in core at any time,
so the size of the data objects is not limited by @acronym{GPGME}.

@deftypefun gpgme_error_t gpgme_data_new_from_fd (@w{gpgme_data_t *@var{dh}}, @w{int @var{fd}})
The function @code{gpgme_data_new_from_fd} creates a new
@code{gpgme_data_t} object and uses the file descriptor @var{fd} to read
from (if used as an input data object) and write to (if used as an
output data object).

When using the data object as an input buffer, the function might read
a bit more from the file descriptor than is actually needed by the
crypto engine in the desired operation because of internal buffering.

Note that GPGME assumes that the file descriptor is set to blocking
mode.  Errors during I/O operations, except for EINTR, are usually
fatal for crypto operations.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_new_from_stream (@w{gpgme_data_t *@var{dh}}, @w{FILE *@var{stream}})
The function @code{gpgme_data_new_from_stream} creates a new
@code{gpgme_data_t} object and uses the I/O stream @var{stream} to read
from (if used as an input data object) and write to (if used as an
output data object).

When using the data object as an input buffer, the function might read
a bit more from the stream than is actually needed by the crypto
engine in the desired operation because of internal buffering.

Note that GPGME assumes that the stream is in blocking mode.  Errors
during I/O operations, except for EINTR, are usually fatal for crypto
operations.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_new_from_estream (@w{gpgme_data_t *@var{dh}}, @w{gpgrt_stream_t @var{stream}})
The function @code{gpgme_data_new_from_estream} creates a new
@code{gpgme_data_t} object and uses the gpgrt stream @var{stream} to read
from (if used as an input data object) and write to (if used as an
output data object).

When using the data object as an input buffer, the function might read
a bit more from the stream than is actually needed by the crypto
engine in the desired operation because of internal buffering.

Note that GPGME assumes that the stream is in blocking mode.  Errors
during I/O operations, except for EINTR, are usually fatal for crypto
operations.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun


@node Callback Based Data Buffers
@subsection Callback Based Data Buffers

If neither memory nor file based data objects are a good fit for your
application, you can implement the functions a data object provides
yourself and create a data object from these callback functions.

@deftp {Data type} {ssize_t (*gpgme_data_read_cb_t) (@w{void *@var{handle}}, @w{void @var{*buffer}}, @w{size_t @var{size}})}
@tindex gpgme_data_read_cb_t
The @code{gpgme_data_read_cb_t} type is the type of functions which
@acronym{GPGME} calls if it wants to read data from a user-implemented
data object.  The function should read up to @var{size} bytes from the
current read position into the space starting at @var{buffer}.  The
@var{handle} is provided by the user at data object creation time.

Note that GPGME assumes that the read blocks until data is available.
Errors during I/O operations, except for EINTR, are usually fatal for
crypto operations.

The function should return the number of bytes read, 0 on EOF, and -1
on error.  If an error occurs, @var{errno} should be set to describe
the type of the error.
@end deftp

@deftp {Data type} {ssize_t (*gpgme_data_write_cb_t) (@w{void *@var{handle}}, @w{const void @var{*buffer}}, @w{size_t @var{size}})}
@tindex gpgme_data_write_cb_t
The @code{gpgme_data_write_cb_t} type is the type of functions which
@acronym{GPGME} calls if it wants to write data to a user-implemented
data object.  The function should write up to @var{size} bytes to the
current write position from the space starting at @var{buffer}.  The
@var{handle} is provided by the user at data object creation time.

Note that GPGME assumes that the write blocks until data is available.
Errors during I/O operations, except for EINTR, are usually fatal for
crypto operations.

The function should return the number of bytes written, and -1 on
error.  If an error occurs, @var{errno} should be set to describe the
type of the error.
@end deftp

@deftp {Data type} {off_t (*gpgme_data_seek_cb_t) (@w{void *@var{handle}}, @w{off_t @var{offset}}, @w{int @var{whence}})}
@tindex gpgme_data_seek_cb_t
The @code{gpgme_data_seek_cb_t} type is the type of functions which
@acronym{GPGME} calls if it wants to change the current read/write
position in a user-implemented data object, just like the @code{lseek}
function.

The function should return the new read/write position, and -1 on
error.  If an error occurs, @var{errno} should be set to describe the
type of the error.
@end deftp

@deftp {Data type} {void (*gpgme_data_release_cb_t) (@w{void *@var{handle}})}
@tindex gpgme_data_release_cb_t
The @code{gpgme_data_release_cb_t} type is the type of functions which
@acronym{GPGME} calls if it wants to destroy a user-implemented data
object.  The @var{handle} is provided by the user at data object
creation time.
@end deftp

@deftp {Data type} {struct gpgme_data_cbs}
This structure is used to store the data callback interface functions
described above.  It has the following members:

@table @code
@item gpgme_data_read_cb_t read
This is the function called by @acronym{GPGME} to read data from the
data object.  It is only required for input data object.

@item gpgme_data_write_cb_t write
This is the function called by @acronym{GPGME} to write data to the
data object.  It is only required for output data object.

@item gpgme_data_seek_cb_t seek
This is the function called by @acronym{GPGME} to change the current
read/write pointer in the data object (if available).  It is optional.

@item gpgme_data_release_cb_t release
This is the function called by @acronym{GPGME} to release a data
object.  It is optional.
@end table
@end deftp

@deftypefun gpgme_error_t gpgme_data_new_from_cbs (@w{gpgme_data_t *@var{dh}}, @w{gpgme_data_cbs_t @var{cbs}}, @w{void *@var{handle}})
The function @code{gpgme_data_new_from_cbs} creates a new
@code{gpgme_data_t} object and uses the user-provided callback functions
to operate on the data object.

The handle @var{handle} is passed as first argument to the callback
functions.  This can be used to identify this data object.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun


@node Destroying Data Buffers
@section Destroying Data Buffers
@cindex data buffer, destruction

@deftypefun void gpgme_data_release (@w{gpgme_data_t @var{dh}})
The function @code{gpgme_data_release} destroys the data object with
the handle @var{dh}.  It releases all associated resources that were
not provided by the user in the first place.
@end deftypefun

@deftypefun {char *} gpgme_data_release_and_get_mem (@w{gpgme_data_t @var{dh}}, @w{size_t *@var{length}})
The function @code{gpgme_data_release_and_get_mem} is like
@code{gpgme_data_release}, except that it returns the data buffer and
its length that was provided by the object.

The user has to release the buffer with @code{gpgme_free}.  In case
the user provided the data buffer in non-copy mode, a copy will be
made for this purpose.

In case an error returns, or there is no suitable data buffer that can
be returned to the user, the function will return @code{NULL}.  In any
case, the data object @var{dh} is destroyed.
@end deftypefun


@deftypefun void gpgme_free (@w{void *@var{buffer}})
@since{1.1.1}

The function @code{gpgme_free} releases the memory returned by
@code{gpgme_data_release_and_get_mem} and
@code{gpgme_pubkey_algo_string}.  It should be used instead of the
system libraries @code{free} function in case different allocators are
used by a program.  This is often the case if gpgme is used under
Windows as a DLL.
@end deftypefun


@node Manipulating Data Buffers
@section Manipulating Data Buffers
@cindex data buffer, manipulation

Data buffers contain data and meta-data.  The following operations can
be used to manipulate both.


@menu
* Data Buffer I/O Operations::    I/O operations on data buffers.
* Data Buffer Meta-Data::         Meta-data manipulation of data buffers.
* Data Buffer Convenience::       Convenience function for data buffers.
@end menu


@node Data Buffer I/O Operations
@subsection Data Buffer I/O Operations
@cindex data buffer, I/O operations
@cindex data buffer, read
@cindex data buffer, write
@cindex data buffer, seek

@deftypefun ssize_t gpgme_data_read (@w{gpgme_data_t @var{dh}}, @w{void *@var{buffer}}, @w{size_t @var{length}})
The function @code{gpgme_data_read} reads up to @var{length} bytes
from the data object with the handle @var{dh} into the space starting
at @var{buffer}.

If no error occurs, the actual amount read is returned.  If the end of
the data object is reached, the function returns 0.

In all other cases, the function returns -1 and sets @var{errno}.
@end deftypefun

@deftypefun ssize_t gpgme_data_write (@w{gpgme_data_t @var{dh}}, @w{const void *@var{buffer}}, @w{size_t @var{size}})
The function @code{gpgme_data_write} writes up to @var{size} bytes
starting from @var{buffer} into the data object with the handle
@var{dh} at the current write position.

The function returns the number of bytes actually written, or -1 if an
error occurs.  If an error occurs, @var{errno} is set.
@end deftypefun

@deftypefun off_t gpgme_data_seek (@w{gpgme_data_t @var{dh}}, @w{off_t @var{offset}}, @w{int @var{whence}})
The function @code{gpgme_data_seek} changes the current read/write
position.

The @var{whence} argument specifies how the @var{offset} should be
interpreted.  It must be one of the following symbolic constants:

@table @code
@item SEEK_SET
Specifies that @var{offset} is a count of characters from the
beginning of the data object.

@item SEEK_CUR
Specifies that @var{offset} is a count of characters from the current
file position.  This count may be positive or negative.

@item SEEK_END
Specifies that @var{offset} is a count of characters from the end of
the data object.  A negative count specifies a position within the
current extent of the data object; a positive count specifies a
position past the current end.  If you set the position past the
current end, and actually write data, you will extend the data object
with zeros up to that position.
@end table

If successful, the function returns the resulting file position,
measured in bytes from the beginning of the data object.  You can use
this feature together with @code{SEEK_CUR} to read the current
read/write position.

If the function fails, -1 is returned and @var{errno} is set.
@end deftypefun


@node Data Buffer Meta-Data
@subsection Data Buffer Meta-Data
@cindex data buffer, meta-data
@cindex data buffer, file name
@cindex data buffer, encoding

@deftypefun {char *} gpgme_data_get_file_name (@w{gpgme_data_t @var{dh}})
@since{1.1.0}

The function @code{gpgme_data_get_file_name} returns a pointer to a
string containing the file name associated with the data object.  The
file name will be stored in the output when encrypting or signing the
data and will be returned to the user when decrypting or verifying the
output data.

If no error occurs, the string containing the file name is returned.
Otherwise, @code{NULL} will be returned.
@end deftypefun


@deftypefun gpgme_error_t gpgme_data_set_file_name (@w{gpgme_data_t @var{dh}}, @w{const char *@var{file_name}})
@since{1.1.0}

The function @code{gpgme_data_set_file_name} sets the file name
associated with the data object.  The file name will be stored in the
output when encrypting or signing the data and will be returned to the
user when decrypting or verifying the output data.

If a signed or encrypted archive is created, then the file name will be
interpreted as the base directory for the relative paths of the files and
directories to put into the archive.  This corresponds to the --directory
option of gpgtar.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{dh} is not a valid pointer and @code{GPG_ERR_ENOMEM} if not
enough memory is available.
@end deftypefun


@deftp {Data type} {enum gpgme_data_encoding_t}
@tindex gpgme_data_encoding_t
The @code{gpgme_data_encoding_t} type specifies the encoding of a
@code{gpgme_data_t} object.  For input data objects, the encoding is
useful to give the backend a hint on the type of data.  For output
data objects, the encoding can specify the output data format on
certain operations.  Please note that not all backends support all
encodings on all operations.  The following data types are available:

@table @code
@item GPGME_DATA_ENCODING_NONE
This specifies that the encoding is not known.  This is the default
for a new data object.  The backend will try its best to detect the
encoding automatically.

@item GPGME_DATA_ENCODING_BINARY
This specifies that the data is encoding in binary form; i.e., there is
no special encoding.

@item GPGME_DATA_ENCODING_BASE64
This specifies that the data is encoded using the Base-64 encoding
scheme as used by @acronym{MIME} and other protocols.

@item GPGME_DATA_ENCODING_ARMOR
This specifies that the data is encoded in an armored form as used by
OpenPGP and PEM.

@item GPGME_DATA_ENCODING_MIME
@since{1.7.0}

This specifies that the data is encoded as a MIME part.

@item GPGME_DATA_ENCODING_URL
@since{1.2.0}

The data is a list of linefeed delimited URLs.  This is only useful with
@code{gpgme_op_import}.

@item GPGME_DATA_ENCODING_URL0
@since{1.2.0}

The data is a list of binary zero delimited URLs.  This is only useful
with @code{gpgme_op_import}.

@item GPGME_DATA_ENCODING_URLESC
@since{1.2.0}

The data is a list of linefeed delimited URLs with all control and space
characters percent escaped.  This mode is is not yet implemented.

@end table
@end deftp

@deftypefun gpgme_data_encoding_t gpgme_data_get_encoding (@w{gpgme_data_t @var{dh}})
The function @code{gpgme_data_get_encoding} returns the encoding of
the data object with the handle @var{dh}.  If @var{dh} is not a valid
pointer (e.g., @code{NULL}) @code{GPGME_DATA_ENCODING_NONE} is
returned.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_set_encoding (@w{gpgme_data_t @var{dh}, gpgme_data_encoding_t @var{enc}})
The function @code{gpgme_data_set_encoding} changes the encoding of
the data object with the handle @var{dh} to @var{enc}.
@end deftypefun

@deftypefun {gpgme_error_t} gpgme_data_set_flag  @
            (@w{gpgme_data_t @var{dh}}, @
            @w{const char *@var{name}}, @
            @w{const char *@var{value}})

@since{1.7.0}

Some minor properties of the data object can be controlled with flags
set by this function.  The properties are identified by the following
values for @var{name}:

@table @code
@item size-hint
The value is a decimal number with the length gpgme shall assume for
this data object.  This is useful if the data is provided by callbacks
or via file descriptors but the applications knows the total size of
the data.  If this is set the OpenPGP engine may use this to decide on
buffer allocation strategies and to provide a total value for its
progress information.

@item io-buffer-size
The value is a decimal number with the length of internal buffers to
used for internal I/O operations.  The value is capped at 1048576 (1
MiB).  In certain environments large buffers can yield a performance
boost for callback bases data object, but the details depend a lot on
the circumstances and the operating system.  This flag may only be set
once and must be set before any actual I/O happens ion the data
objects.

@item sensitive
If the numeric value is not 0 the data object is considered to contain
sensitive information like passwords or key material.  If this is set
the internal buffers are securely overwritten with zeroes by
gpgme_data_release.


@end table

This function returns @code{0} on success.
@end deftypefun


@node Data Buffer Convenience
@subsection Data Buffer Convenience Functions
@cindex data buffer, convenience
@cindex type of data
@cindex identify

@deftp {Data type} {enum gpgme_data_type_t}
@tindex gpgme_data_type_t
@since{1.4.3}

The @code{gpgme_data_type_t} type is used to return the detected type
of the content of a data buffer.
@end deftp

@table @code
@item GPGME_DATA_TYPE_INVALID
This is returned by @code{gpgme_data_identify} if it was not possible
to identify the data.  Reasons for this might be a non-seekable stream
or a memory problem.  The value is 0.
@item GPGME_DATA_TYPE_UNKNOWN
The type of the data is not known.
@item GPGME_DATA_TYPE_PGP_SIGNED
The data is an OpenPGP signed message.  This may be a binary
signature, a detached one or a cleartext signature.
@item GPGME_DATA_TYPE_PGP_ENCRYPTED
@since{1.7.0}

The data is an OpenPGP encrypted message.
@item GPGME_DATA_TYPE_PGP_SIGNATURE
@since{1.7.0}

The data is an OpenPGP detached signature.
@item GPGME_DATA_TYPE_PGP_OTHER
This is a generic OpenPGP message.  In most cases this will be
encrypted data.
@item GPGME_DATA_TYPE_PGP_KEY
This is an OpenPGP key (private or public).
@item GPGME_DATA_TYPE_CMS_SIGNED
This is a CMS signed message.
@item GPGME_DATA_TYPE_CMS_ENCRYPTED
This is a CMS encrypted (enveloped data) message.
@item GPGME_DATA_TYPE_CMS_OTHER
This is used for other CMS message types.
@item GPGME_DATA_TYPE_X509_CERT
The data is a X.509 certificate
@item GPGME_DATA_TYPE_PKCS12
The data is a PKCS#12 message.  This is commonly used to exchange
private keys for X.509.
@end table

@deftypefun gpgme_data_type_t gpgme_data_identify (@w{gpgme_data_t @var{dh}, int @var{reserved}})
@since{1.4.3}

The function @code{gpgme_data_identify} returns the type of the data
with the handle @var{dh}.  If it is not possible to perform the
identification, the function returns zero
(@code{GPGME_DATA_TYPE_INVALID}).  Note that depending on how the data
object has been created the identification may not be possible or the
data object may change its internal state (file pointer moved).  For
file or memory based data object, the state should not change.
@var{reserved} should be zero.
@end deftypefun


@c
@c    Chapter Contexts
@c
@node Contexts
@chapter Contexts
@cindex context

All cryptographic operations in @acronym{GPGME} are performed within a
context, which contains the internal state of the operation as well as
configuration parameters.  By using several contexts you can run
several cryptographic operations in parallel, with different
configuration.

@deftp {Data type} {gpgme_ctx_t}
The @code{gpgme_ctx_t} type is a handle for a @acronym{GPGME} context,
which is used to hold the configuration, status and result of
cryptographic operations.
@end deftp

@menu
* Creating Contexts::             Creating new @acronym{GPGME} contexts.
* Destroying Contexts::           Releasing @acronym{GPGME} contexts.
* Result Management::             Managing the result of crypto operations.
* Context Attributes::            Setting properties of a context.
* Key Management::                Managing keys with @acronym{GPGME}.
* Crypto Operations::             Using a context for cryptography.
* Miscellaneous::                 Miscellaneous operations
* Run Control::                   Controlling how operations are run.
@end menu


@node Creating Contexts
@section Creating Contexts
@cindex context, creation

@deftypefun gpgme_error_t gpgme_new (@w{gpgme_ctx_t *@var{ctx}})
The function @code{gpgme_new} creates a new @code{gpgme_ctx_t} object
and returns a handle for it in @var{ctx}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
context was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and @code{GPG_ERR_ENOMEM} if not
enough memory is available.  Also, it returns
@code{GPG_ERR_NOT_OPERATIONAL} if @code{gpgme_check_version} was not
called to initialize GPGME, and @code{GPG_ERR_SELFTEST_FAILED} if a
selftest failed.  Currently, the only selftest is for Windows MingW32
targets to see if @code{-mms-bitfields} was used (as required).
@end deftypefun


@node Destroying Contexts
@section Destroying Contexts
@cindex context, destruction

@deftypefun void gpgme_release (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_release} destroys the context with the handle
@var{ctx} and releases all associated resources.
@end deftypefun


@node Result Management
@section Result Management
@cindex context, result of operation

The detailed result of an operation is returned in operation-specific
structures such as @code{gpgme_decrypt_result_t}.  The corresponding
retrieval functions such as @code{gpgme_op_decrypt_result} provide
static access to the results after an operation completes.  Those
structures shall be considered read-only and an application must not
allocate such a structure on its own.  The following interfaces make
it possible to detach a result structure from its associated context
and give it a lifetime beyond that of the current operation or
context.

@deftypefun void gpgme_result_ref (@w{void *@var{result}})
@since{1.2.0}

The function @code{gpgme_result_ref} acquires an additional reference
for the result @var{result}, which may be of any type
@code{gpgme_*_result_t}.  As long as the user holds a reference, the
result structure is guaranteed to be valid and unmodified.
@end deftypefun

@deftypefun void gpgme_result_unref (@w{void *@var{result}})
@since{1.2.0}

The function @code{gpgme_result_unref} releases a reference for the
result @var{result}.  If this was the last reference, the result
structure will be destroyed and all resources associated to it will be
released.
@end deftypefun

Note that a context may hold its own references to result structures,
typically until the context is destroyed or the next operation is
started.  In fact, these references are accessed through the
@code{gpgme_op_*_result} functions.


@node Context Attributes
@section Context Attributes
@cindex context, attributes

@menu
* Protocol Selection::            Selecting the protocol used by a context.
* Crypto Engine::                 Configuring the crypto engine.
* Setting the Sender::            How to tell the engine the sender.
* ASCII Armor::                   Requesting @acronym{ASCII} armored output.
* Text Mode::                     Choosing canonical text mode.
* Offline Mode::                  Choosing offline mode.
* Pinentry Mode::                 Choosing the pinentry mode.
* Included Certificates::         Including a number of certificates.
* Key Listing Mode::              Selecting key listing mode.
* Passphrase Callback::           Getting the passphrase from the user.
* Progress Meter Callback::       Being informed about the progress.
* Status Message Callback::       Status messages received from gpg.
* Context Flags::                 Additional flags for a context.
* Locale::                        Setting the locale of a context.
* Additional Logs::               Additional logs of a context.
@end menu


@node Protocol Selection
@subsection Protocol Selection
@cindex context, selecting protocol
@cindex protocol, selecting

@deftypefun gpgme_error_t gpgme_set_protocol (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_protocol_t @var{proto}})
The function @code{gpgme_set_protocol} sets the protocol used within
the context @var{ctx} to @var{proto}.  All crypto operations will be
performed by the crypto engine configured for that protocol.
@xref{Protocols and Engines}.

Setting the protocol with @code{gpgme_set_protocol} does intentionally
not check if the crypto engine for that protocol is available and
installed correctly.  @xref{Engine Version Check}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
protocol could be set successfully, and @code{GPG_ERR_INV_VALUE} if
@var{protocol} is not a valid protocol.
@end deftypefun

@deftypefun gpgme_protocol_t gpgme_get_protocol (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_get_protocol} retrieves the protocol currently
use with the context @var{ctx}.
@end deftypefun


@node Crypto Engine
@subsection Crypto Engine
@cindex context, configuring engine
@cindex engine, configuration per context

The following functions can be used to set and retrieve the
configuration of the crypto engines of a specific context.  The
default can also be retrieved without any particular context.
@xref{Engine Information}.  The default can also be changed globally.
@xref{Engine Configuration}.

@deftypefun gpgme_engine_info_t gpgme_ctx_get_engine_info (@w{gpgme_ctx_t @var{ctx}})
@since{1.1.0}

The function @code{gpgme_ctx_get_engine_info} returns a linked list of
engine info structures.  Each info structure describes the
configuration of one configured backend, as used by the context
@var{ctx}.

The result is valid until the next invocation of
@code{gpgme_ctx_set_engine_info} for this particular context.

This function can not fail.
@end deftypefun

@deftypefun gpgme_error_t gpgme_ctx_set_engine_info (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_protocol_t @var{proto}}, @w{const char *@var{file_name}}, @w{const char *@var{home_dir}})
@since{1.1.0}

The function @code{gpgme_ctx_set_engine_info} changes the
configuration of the crypto engine implementing the protocol
@var{proto} for the context @var{ctx}.

@var{file_name} is the file name of the executable program
implementing this protocol, and @var{home_dir} is the directory name
of the configuration directory for this crypto engine.  If
@var{home_dir} is @code{NULL}, the engine's default will be used.

Currently this function must be used before starting the first crypto
operation.  It is unspecified if and when the changes will take effect
if the function is called after starting the first operation on the
context @var{ctx}.

This function returns the error code @code{GPG_ERR_NO_ERROR} if
successful, or an error code on failure.
@end deftypefun


@node Setting the Sender
@subsection How to tell the engine the sender.
@cindex context, sender
@cindex sender
@cindex From:

Some engines can make use of the sender’s address, for example to
figure out the best user id in certain trust models.  For verification
and signing of mails, it is thus suggested to let the engine know the
sender ("From:") address.  @acronym{GPGME} provides two functions to
accomplish that.  Note that the esoteric use of multiple "From:"
addresses is not supported.

@deftypefun gpgme_error_t gpgme_set_sender @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{int @var{address}})

@since{1.8.0}

The function @code{gpgme_set_sender} specifies the sender address for
use in sign and verify operations.  @var{address} is expected to be
the ``addr-spec'' part of an address but may also be a complete mailbox
address, in which case this function extracts the ``addr-spec'' from
it.  Using @code{NULL} for @var{address} clears the sender address.

The function returns 0 on success or an error code on failure.  The
most likely failure is that no valid ``addr-spec'' was found in
@var{address}.

@end deftypefun

@deftypefun @w{const char *} gpgme_get_sender @
      (@w{gpgme_ctx_t @var{ctx}})

@since{1.8.0}

The function @code{gpgme_get_sender} returns the current sender
address from the context, or NULL if none was set.  The returned
value is valid as long as the @var{ctx} is valid and
@code{gpgme_set_sender} has not been called again.

@end deftypefun



@c FIXME: Unfortunately, using @acronym here breaks texi2dvi.
@node ASCII Armor
@subsection @acronym{ASCII} Armor
@cindex context, armor mode
@cindex @acronym{ASCII} armor
@cindex armor mode

@deftypefun void gpgme_set_armor (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{yes}})
The function @code{gpgme_set_armor} specifies if the output should be
@acronym{ASCII} armored.  By default, output is not @acronym{ASCII}
armored.

@acronym{ASCII} armored output is disabled if @var{yes} is zero, and
enabled otherwise.
@end deftypefun

@deftypefun int gpgme_get_armor (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_get_armor} returns 1 if the output is
@acronym{ASCII} armored, and @code{0} if it is not, or if @var{ctx} is
not a valid pointer.
@end deftypefun


@node Text Mode
@subsection Text Mode
@cindex context, text mode
@cindex text mode
@cindex canonical text mode

@deftypefun void gpgme_set_textmode (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{yes}})
The function @code{gpgme_set_textmode} specifies if canonical text mode
should be used.  By default, text mode is not used.

Text mode is for example used for the RFC2015 signatures; note that
the updated RFC 3156 mandates that the mail user agent does some
preparations so that text mode is not needed anymore.

This option is only relevant to the OpenPGP crypto engine, and ignored
by all other engines.

Canonical text mode is disabled if @var{yes} is zero, and enabled
otherwise.
@end deftypefun

@deftypefun int gpgme_get_textmode (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_get_textmode} returns 1 if canonical text
mode is enabled, and @code{0} if it is not, or if @var{ctx} is not a
valid pointer.
@end deftypefun


@node Offline Mode
@subsection Offline Mode
@cindex context, offline mode
@cindex offline mode

@deftypefun void gpgme_set_offline (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{yes}})
@since{1.6.0}

The function @code{gpgme_set_offline} specifies if offline mode should
be used.  Offline mode is disabled if @var{yes} is zero, and enabled
otherwise.  By default, offline mode is disabled.

The details of the offline mode depend on the used protocol and its
backend engine.  It may eventually be extended to be more stricter and
for example completely disable the use of Dirmngr for any engine.

For the CMS protocol the offline mode specifies whether Dirmngr shall
be used to do additional validation that might require connecting
external services (e.g., CRL / OCSP checks).  The offline mode is used
for all operations on this context.  It has only an effect with
GnuPG versions 2.1.6 or later.

For the OpenPGP protocol offline mode entirely disables the use of the
Dirmngr and will thus guarantee that no network connections are done
as part of an operation on this context.  It has only an effect with
GnuPG versions 2.1.23 or later.

For all other protocols the offline mode is currently ignored.

@end deftypefun

@deftypefun int gpgme_get_offline (@w{gpgme_ctx_t @var{ctx}})
@since{1.6.0}

The function @code{gpgme_get_offline} returns 1 if offline
mode is enabled, and @code{0} if it is not, or if @var{ctx} is not a
valid pointer.
@end deftypefun


@node Pinentry Mode
@subsection Pinentry Mode
@cindex context, pinentry mode
@cindex pinentry mode

@deftypefun gpgme_error_t gpgme_set_pinentry_mode (@w{gpgme_ctx_t @var{ctx}},
@w{gpgme_pinentry_mode_t @var{mode}})

@since{1.4.0}

The function @code{gpgme_set_pinentry_mode} specifies the pinentry mode
to be used.

For GnuPG >= 2.1 this option is required to be set to
@code{GPGME_PINENTRY_MODE_LOOPBACK} to enable the passphrase callback
mechanism in GPGME through @code{gpgme_set_passphrase_cb}.
@end deftypefun

@deftypefun gpgme_pinentry_mode_t gpgme_get_pinentry_mode (@w{gpgme_ctx_t @var{ctx}})
@since{1.4.0}

The function @code{gpgme_get_pinenty_mode} returns the
mode set for the context.
@end deftypefun

@deftp {Data type} {enum gpgme_pinentry_mode_t}
@tindex gpgme_pinentry_mode_t
@since{1.4.0}

The @code{gpgme_minentry_mode_t} type specifies the set of possible pinentry
modes that are supported by @acronym{GPGME} if GnuPG >= 2.1 is used.
The following modes are supported:

@table @code
@item GPGME_PINENTRY_MODE_DEFAULT
@since{1.4.0}

Use the default of the agent, which is ask.

@item GPGME_PINENTRY_MODE_ASK
@since{1.4.0}

Force the use of the Pinentry.

@item GPGME_PINENTRY_MODE_CANCEL
@since{1.4.0}

Emulate use of Pinentry's cancel button.

@item GPGME_PINENTRY_MODE_ERROR
@since{1.4.0}

Return a Pinentry error @code{No Pinentry}.

@item GPGME_PINENTRY_MODE_LOOPBACK
@since{1.4.0}

Redirect Pinentry queries to the caller.
This enables the use of @code{gpgme_set_passphrase_cb} because pinentry
queries are redirected to gpgme.

Note: For 2.1.0 - 2.1.12 this mode requires @code{allow-loopback-pinentry}
to be enabled in the @file{gpg-agent.conf} or an agent started with that option.

@end table
@end deftp


@node Included Certificates
@subsection Included Certificates
@cindex certificates, included

@deftypefun void gpgme_set_include_certs (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{nr_of_certs}})
The function @code{gpgme_set_include_certs} specifies how many
certificates should be included in an S/MIME signed message.  By
default, only the sender's certificate is included.  The possible
values of @var{nr_of_certs} are:

@table @code
@item GPGME_INCLUDE_CERTS_DEFAULT
@since{1.0.3}

Fall back to the default of the crypto backend.  This is the default
for GPGME.
@item -2
Include all certificates except the root certificate.
@item -1
Include all certificates.
@item 0
Include no certificates.
@item 1
Include the sender's certificate only.
@item n
Include the first n certificates of the certificates path, starting
from the sender's certificate.  The number @code{n} must be positive.
@end table

Values of @var{nr_of_certs} smaller than -2 are undefined.

This option is only relevant to the CMS crypto engine, and ignored by
all other engines.
@end deftypefun

@deftypefun int gpgme_get_include_certs (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_get_include_certs} returns the number of
certificates to include into an S/MIME signed message.
@end deftypefun


@node Key Listing Mode
@subsection Key Listing Mode
@cindex key listing mode
@cindex key listing, mode of

@deftypefun gpgme_error_t gpgme_set_keylist_mode (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_keylist_mode_t @var{mode}})
The function @code{gpgme_set_keylist_mode} changes the default
behaviour of the key listing functions.  The value in @var{mode} is a
bitwise-or combination of one or multiple of the following bit values:

@table @code
@item GPGME_KEYLIST_MODE_LOCAL
The @code{GPGME_KEYLIST_MODE_LOCAL} symbol specifies that the local
keyring should be searched for keys in the keylisting operation.  This
is the default.

Using only this option results in a @code{--list-keys}.

@item GPGME_KEYLIST_MODE_EXTERN
The @code{GPGME_KEYLIST_MODE_EXTERN} symbol specifies that an external
source should be searched for keys in the keylisting operation.  The
type of external source is dependent on the crypto engine used and
whether it is combined with @code{GPGME_KEYLIST_MODE_LOCAL}.  For
example, it can be a remote keyserver or LDAP certificate server.

Using only this option results in a @code{--search-keys} for
@code{GPGME_PROTOCOL_OpenPGP} and something similar to
@code{--list-external-keys} for @code{GPGME_PROTOCOL_CMS}.

@item GPGME_KEYLIST_MODE_LOCATE
This is a shortcut for the combination of
@code{GPGME_KEYLIST_MODE_LOCAL} and @code{GPGME_KEYLIST_MODE_EXTERN}, which
results in a @code{--locate-keys} for @code{GPGME_PROTOCOL_OpenPGP}.

@item GPGME_KEYLIST_MODE_SIGS
The @code{GPGME_KEYLIST_MODE_SIGS} symbol specifies that the key
signatures should be included in the listed keys.

@item GPGME_KEYLIST_MODE_SIG_NOTATIONS
@since{1.1.1}

The @code{GPGME_KEYLIST_MODE_SIG_NOTATIONS} symbol specifies that the
signature notations on key signatures should be included in the listed
keys.  This only works if @code{GPGME_KEYLIST_MODE_SIGS} is also
enabled.

@item GPGME_KEYLIST_MODE_WITH_TOFU
@since{1.7.0}

The @code{GPGME_KEYLIST_MODE_WITH_TOFU} symbol specifies that
information pertaining to the TOFU trust model should be included in
the listed keys.

@item GPGME_KEYLIST_MODE_WITH_KEYGRIP
@since{1.14.0}

The @code{GPGME_KEYLIST_MODE_WITH_KEYRIP} symbol specifies that the
keygrip is always included in the listing.  The default depends on the
version of the backend and the used protocol.

@item GPGME_KEYLIST_MODE_WITH_SECRET
@since{1.5.1}

The @code{GPGME_KEYLIST_MODE_WITH_SECRET} returns information about
the presence of a corresponding secret key in a public key listing.  A
public key listing with this mode is slower than a standard listing
but can be used instead of a second run to list the secret keys.  This
is only supported for GnuPG versions >= 2.1.  Note that using this
option also makes sure that the keygrip is available in the output.

@item GPGME_KEYLIST_MODE_EPHEMERAL
@since{1.2.0}

The @code{GPGME_KEYLIST_MODE_EPHEMERAL} symbol specifies that keys
flagged as ephemeral are included in the listing.

@item GPGME_KEYLIST_MODE_WITH_V5FPR
@since{1.23.0}

The @code{GPGME_KEYLIST_MODE_WITH_V5FPR} symbol specifies that key
listings shall also provide v5 style fingerprints for v4 OpenPGp keys.

@item GPGME_KEYLIST_MODE_VALIDATE
@since{0.4.5}

The @code{GPGME_KEYLIST_MODE_VALIDATE} symbol specifies that the
backend should do key or certificate validation and not just get the
validity information from an internal cache.  This might be an
expensive operation and is in general not useful.  Currently only
implemented for the S/MIME backend and ignored for other backends.

@item GPGME_KEYLIST_MODE_FORCE_EXTERN
@since{1.18.0}

The @code{GPGME_KEYLIST_MODE_FORCE_EXTERN} symbol specifies that only
external sources should be searched for keys in the keylisting
operation.  If used in combination with @code{GPGME_KEYLIST_MODE_LOCATE},
the keylisting results in a @code{--locate-external-keys} for
@code{GPGME_PROTOCOL_OpenPGP}.  The combination with
@code{GPGME_KEYLIST_MODE_LOCAL}, but without @code{GPGME_KEYLIST_MODE_EXTERN}
is not allowed.  Currently only implemented for the OpenPGP backend and
ignored for other backends.

@item GPGME_KEYLIST_MODE_LOCATE_EXTERNAL
@since{1.18.0}

This is a shortcut for the combination of
@code{GPGME_KEYLIST_MODE_LOCATE} and @code{GPGME_KEYLIST_MODE_FORCE_EXTERN},
which results in a @code{--locate-external-keys} for
@code{GPGME_PROTOCOL_OpenPGP}.

@end table

At least one of @code{GPGME_KEYLIST_MODE_LOCAL} and
@code{GPGME_KEYLIST_MODE_EXTERN} must be specified.  For future binary
compatibility, you should get the current mode with
@code{gpgme_get_keylist_mode} and modify it by setting or clearing the
appropriate bits, and then using that calculated value in the
@code{gpgme_set_keylisting_mode} operation.  This will leave all other
bits in the mode value intact (in particular those that are not used
in the current version of the library).

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
mode could be set correctly, and @code{GPG_ERR_INV_VALUE} if @var{ctx}
is not a valid pointer or @var{mode} is not a valid mode.
@end deftypefun


@deftypefun gpgme_keylist_mode_t gpgme_get_keylist_mode (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_get_keylist_mode} returns the current key
listing mode of the context @var{ctx}.  This value can then be
modified and used in a subsequent @code{gpgme_set_keylist_mode}
operation to only affect the desired bits (and leave all others
intact).

The function returns 0 if @var{ctx} is not a valid pointer, and the
current mode otherwise.  Note that 0 is not a valid mode value.
@end deftypefun


@node Passphrase Callback
@subsection Passphrase Callback
@cindex callback, passphrase
@cindex passphrase callback

@deftp {Data type} {gpgme_error_t (*gpgme_passphrase_cb_t)(void *@var{hook}, const char *@var{uid_hint}, const char *@var{passphrase_info}, @w{int @var{prev_was_bad}}, @w{int @var{fd}})}
@tindex gpgme_passphrase_cb_t
The @code{gpgme_passphrase_cb_t} type is the type of functions usable as
passphrase callback function.

The argument @var{uid_hint} might contain a string that gives an
indication for which user ID the passphrase is required.  If this is
not available, or not applicable (in the case of symmetric encryption,
for example), @var{uid_hint} will be @code{NULL}.

The argument @var{passphrase_info}, if not @code{NULL}, will give
further information about the context in which the passphrase is
required.  This information is engine and operation specific.

If this is the repeated attempt to get the passphrase, because
previous attempts failed, then @var{prev_was_bad} is 1, otherwise it
will be 0.

The user must write the passphrase, followed by a newline character,
to the file descriptor @var{fd}.  The function @code{gpgme_io_writen}
should be used for the write operation.  Note that if the user returns
0 to indicate success, the user must at least write a newline
character before returning from the callback.

If an error occurs, return the corresponding @code{gpgme_error_t}
value.  You can use the error code @code{GPG_ERR_CANCELED} to abort
the operation.  Otherwise, return @code{0}.

Note: The passphrase_cb only works with GnuPG 1.x and 2.1.x and not
with the 2.0.x series. See @code{gpgme_set_pinentry_mode} for more
details on 2.1.x usage.
@end deftp

@deftypefun void gpgme_set_passphrase_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_passphrase_cb_t @var{passfunc}}, @w{void *@var{hook_value}})
The function @code{gpgme_set_passphrase_cb} sets the function that is
used when a passphrase needs to be provided by the user to
@var{passfunc}.  The function @var{passfunc} needs to implemented by
the user, and whenever it is called, it is called with its first
argument being @var{hook_value}.  By default, no passphrase callback
function is set.

Not all crypto engines require this callback to retrieve the
passphrase.  It is better if the engine retrieves the passphrase from
a trusted agent (a daemon process), rather than having each user to
implement their own passphrase query.  Some engines do not even
support an external passphrase callback at all, in this case the error
code @code{GPG_ERR_NOT_SUPPORTED} is returned.

For GnuPG >= 2.1 the pinentry mode has to be set to
@code{GPGME_PINENTRY_MODE_LOOPBACK} to enable the passphrase callback.
See @code{gpgme_set_pinentry_mode}.

The user can disable the use of a passphrase callback function by
calling @code{gpgme_set_passphrase_cb} with @var{passfunc} being
@code{NULL}.
@end deftypefun

@deftypefun void gpgme_get_passphrase_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_passphrase_cb_t *@var{passfunc}}, @w{void **@var{hook_value}})
The function @code{gpgme_get_passphrase_cb} returns the function that
is used when a passphrase needs to be provided by the user in
@var{*passfunc}, and the first argument for this function in
@var{*hook_value}.  If no passphrase callback is set, or @var{ctx} is
not a valid pointer, @code{NULL} is returned in both variables.

@var{passfunc} or @var{hook_value} can be @code{NULL}.  In this case,
the corresponding value will not be returned.
@end deftypefun


@node Progress Meter Callback
@subsection Progress Meter Callback
@cindex callback, progress meter
@cindex progress meter callback

@deftp {Data type} {void (*gpgme_progress_cb_t)(void *@var{hook}, const char *@var{what}, int @var{type}, int @var{current}, int @var{total})}
@tindex gpgme_progress_cb_t
The @code{gpgme_progress_cb_t} type is the type of functions usable as
progress callback function.

The arguments are specific to the crypto engine.  More information
about the progress information returned from the GnuPG engine can be
found in the GnuPG source code in the file @file{doc/DETAILS} in the
section PROGRESS.
@end deftp

@deftypefun void gpgme_set_progress_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_progress_cb_t @var{progfunc}}, @w{void *@var{hook_value}})
The function @code{gpgme_set_progress_cb} sets the function that is
used when progress information about a cryptographic operation is
available.  The function @var{progfunc} needs to implemented by the
user, and whenever it is called, it is called with its first argument
being @var{hook_value}.  By default, no progress callback function
is set.

Setting a callback function allows an interactive program to display
progress information about a long operation to the user.

The user can disable the use of a progress callback function by
calling @code{gpgme_set_progress_cb} with @var{progfunc} being
@code{NULL}.
@end deftypefun

@deftypefun void gpgme_get_progress_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_progress_cb_t *@var{progfunc}}, @w{void **@var{hook_value}})
The function @code{gpgme_get_progress_cb} returns the function that is
used to inform the user about the progress made in @var{*progfunc},
and the first argument for this function in @var{*hook_value}.  If no
progress callback is set, or @var{ctx} is not a valid pointer,
@code{NULL} is returned in both variables.

@var{progfunc} or @var{hook_value} can be @code{NULL}.  In this case,
the corresponding value will not be returned.
@end deftypefun


@node Status Message Callback
@subsection Status Message Callback
@cindex callback, status message
@cindex status message callback

@deftp {Data type} {gpgme_error_t (*gpgme_status_cb_t)(void *@var{hook}, const char *@var{keyword}, const char *@var{args})}
@tindex gpgme_status_cb_t
The @code{gpgme_status_cb_t} type is the type of function usable as
a status message callback function.

The argument @var{keyword} is the name of the status message while the
@var{args} argument contains any arguments for the status message.

If an error occurs, return the corresponding @code{gpgme_error_t}
value. Otherwise, return @code{0}.
@end deftp

@deftypefun void gpgme_set_status_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_status_cb_t @var{statusfunc}}, @w{void *@var{hook_value}})
@since{1.6.0}

The function @code{gpgme_set_status_cb} sets the function that is used when a
status message is received from gpg to @var{statusfunc}. The function
@var{statusfunc} needs to be implemented by the user, and whenever it is
called, it is called with its first argument being @var{hook_value}.  By
default, no status message callback function is set.

The user can disable the use of a status message callback function by calling
@code{gpgme_set_status_cb} with @var{statusfunc} being @code{NULL}.
@end deftypefun

@deftypefun void gpgme_get_status_cb (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_status_cb_t *@var{statusfunc}}, @w{void **@var{hook_value}})
@since{1.6.0}

The function @code{gpgme_get_status_cb} returns the function that is used to
process status messages from gpg in @var{*statusfunc}, and the first argument
for this function in @var{*hook_value}.  If no status message callback is set,
or @var{ctx} is not a valid pointer, @code{NULL} is returned in both
variables.
@end deftypefun

@node Context Flags
@subsection Context Flags
@cindex flags, of a context

@deftypefun {gpgme_error_t} gpgme_set_ctx_flag  @
            (@w{gpgme_ctx_t @var{ctx}}, @
            @w{const char *@var{name}}, @
            @w{const char *@var{value}})

@since{1.7.0}

Some minor properties of the context can be controlled with flags set
by this function.  The properties are identified by the following
values for @var{name}:

@table @code
@item "redraw"
This flag is normally not changed by the caller because GPGME sets and
clears it automatically: The flag is cleared before an operation and
set if an operation noticed that the engine has launched a Pinentry.
A Curses based application may use this information to redraw the
screen; for example:

@example
    err = gpgme_op_keylist_start (ctx, "foo@@example.org", 0);
    while (!err)
      @{
        err = gpgme_op_keylist_next (ctx, &key);
        if (err)
          break;
        show_key (key);
        gpgme_key_release (key);
      @}
    if ((s = gpgme_get_ctx_flag (ctx, "redraw")) && *s)
      redraw_screen ();
    gpgme_release (ctx);
@end example


@item "full-status"
Using a @var{value} of "1" the status callback set by
gpgme_set_status_cb returns all status lines with the exception of
PROGRESS lines.  With the default of "0" the status callback is only
called in certain situations.

@item "raw-description"
Setting the @var{value} to "1" returns human readable strings in a raw
format.  For example the non breaking space characters ("~") will not
be removed from the @code{description} field of the
@code{gpgme_tofu_info_t} object.

@item "export-session-key"
Using a @var{value} of "1" specifies that the context should try to
export the symmetric session key when decrypting data.  By default, or
when using an empty string or "0" for @var{value}, session keys are
not exported.

@item "override-session-key"
The string given in @var{value} is passed to the GnuPG engine to override
the session key for decryption.  The format of that session key is
specific to GnuPG and can be retrieved during a decrypt operation when
the context flag "export-session-key" is enabled.  Please be aware that
using this feature with GnuPG < 2.1.16 or when decrypting an archive
will leak the session key on many platforms via ps(1).

@item "auto-key-retrieve"
Setting the @var{value} to "1" asks the backend to automatically
retrieve a key for signature verification if possible.  Note that this
option makes a "web bug" like behavior possible.  Keyserver or Web Key
Directory operators can see which keys you request, so by sending you
a message signed by a brand new key (which you naturally will not have
on your local keyring), the operator can tell both your IP address and
the time when you verified the signature.

@item "auto-key-import"
Setting the @var{value} to "1" forces the GPG backend to automatically
import a missing key for signature verification from the signature.

@item "include-key-block"
Setting the @var{value} to "1" forces the GPG backend to embed the
signing key as well as an encryption subkey into the the signature.

@item "request-origin"
The string given in @var{value} is passed to the GnuPG engines to
request restrictions based on the origin of the request.  Valid values
are documented in the GnuPG manual and the gpg man page under the
option @option{--request-origin}.  Requires at least GnuPG 2.2.6 to have an
effect.

@item "no-symkey-cache"
For OpenPGP disable the passphrase cache used for symmetrical en- and
decryption.  This cache is based on the message specific salt value.
Requires at least GnuPG 2.2.7 to have an effect.

@item "ignore-mdc-error"
This flag passes the option @option{--ignore-mdc-error} to gpg.  This
can be used to force decryption of a message which failed due to a
missing integrity check.  This flag must be used with great caution
and only if it is a known non-corrupted old message and the decryption
result of the former try had the decryption result flag
@code{legacy_cipher_nomdc} set.  For failsafe reasons this flag is
reset after each operation.

@item "auto-key-locate"
The string given in @var{value} is passed to gpg.  This can be used
to change the behavior of a @code{GPGME_KEYLIST_MODE_LOCATE} keylisting.
Valid values are documented in the GnuPG manual and the gpg man page under
the option @option{--auto-key-locate}.
Requires at least GnuPG 2.1.18.

Note: Keys retrieved through @code{auto-key-locate} are automatically
imported in the keyring.

@item trust-model
@since{1.11.2}

Change the trust-model for all GnuPG engine operations.  An empty
string sets the trust-model back to the users default.  If the
trust-model is not supported by GnuPG the behavior is undefined
and will likely cause all operations to fail.  Example: "tofu+pgp".

This options should be used carefully with a strict version
requirement.  In some versions of GnuPG setting the
trust-model changes the default trust-model for future operations.
A change in the trust-model also can have unintended side effects, like
rebuilding the trust-db.

@item "extended-edit"
This flag passes the option @option{--expert} to gpg key edit.  This
can be used to get additional callbacks in @code{gpgme_op_edit}.

@item "cert-expire"
@since{1.15.2}
The string given in @var{value} is passed to the GnuPG engine to set
the expiration time to use for key signature expiration.  Valid values
are documented in the GnuPG manual and the gpg man page under
the option @option{--default-cert-expire}.

@item "key-origin"
@since{1.16.1}
The string given in @var{value} is passed to the GnuPG engine to set
the origin of imported keys.  Valid values are documented in the GnuPG
manual and the gpg man page under the option @option{--key-origin}.

@item "import-filter"
@since{1.16.1}
The string given in @var{value} is passed to the GnuPG engine to use as
filter when importing keys.  Valid values are documented in the GnuPG
manual and the gpg man page under the option @option{--import-filter}.

@item "import-options"
@since{1.24.0}
The string given in @var{value} is passed to the GnuPG engine to use as
options when importing keys.  Valid values are documented in the GnuPG
manual and the gpg man page under the option @option{--import-options}.

@item "no-auto-check-trustdb"
@since{1.19.0}
Setting the @var{value} to "1" forces the GPG backend to disable the
automatic check of the trust database.

@item "proc-all-sigs"
@since{1.24.0}
Setting the @var{value} to "1" forces the GPG backend not to stop
signature checking of data after a bad signatures.  This option is
ignored if the backend itself does not support the --proc-all-sigs
option.

@item "known-notations"
@since{1.24.0}
The @var{value} is a space or comma delimited list of notation names
which will be used to create @option{--known-notation} options for gpg.


@end table

This function returns @code{0} on success.
@end deftypefun


@deftypefun {const char *} gpgme_get_ctx_flag  @
            (@w{gpgme_ctx_t @var{ctx}}, @
            @w{const char *@var{name}})

@since{1.8.0}

The value of flags settable by @code{gpgme_set_ctx_flag} can be
retrieved by this function.  If @var{name} is unknown the function
returns @code{NULL}.  For boolean flags an empty string is returned
for False and the string "1" is returned for True; either atoi(3) or a
test for an empty string can be used to get the boolean value.

@end deftypefun


@node Locale
@subsection Locale
@cindex locale, default
@cindex locale, of a context

A locale setting can be associated with a context.  This locale is
passed to the crypto engine, and used for applications like the PIN
entry, which is displayed to the user when entering a passphrase is
required.

The default locale is used to initialize the locale setting of all
contexts created afterwards.

@deftypefun gpgme_error_t gpgme_set_locale (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{category}}, @w{const char *@var{value}})
@since{0.4.3}

The function @code{gpgme_set_locale} sets the locale of the context
@var{ctx}, or the default locale if @var{ctx} is a null pointer.

The locale settings that should be changed are specified by
@var{category}.  Supported categories are @code{LC_CTYPE},
@code{LC_MESSAGES}, and @code{LC_ALL}, which is a wildcard you can use
if you want to change all the categories at once.

The value to be used for the locale setting is @var{value}, which will
be copied to @acronym{GPGME}'s internal data structures.  @var{value}
can be a null pointer, which disables setting the locale, and will
make PIN entry and other applications use their default setting, which
is usually not what you want.

Note that the settings are only used if the application runs on a text
terminal, and that the settings should fit the configuration of the
output terminal.  Normally, it is sufficient to initialize the default
value at startup.

The function returns an error if not enough memory is available.
@end deftypefun


@node Additional Logs
@subsection Additional Logs
@cindex auditlog, of the engine
@cindex auditlog

Additional logs can be associated with a context.  These logs are
engine specific and can be be obtained with @code{gpgme_op_getauditlog}.

@deftypefun gpgme_error_t gpgme_op_getauditlog @
            (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{output}}, @
            @w{unsigned int @var{flags}})
@since{1.1.1}

The function @code{gpgme_op_getauditlog} is used to obtain additional
logs as specified by @var{flags} into the @var{output} data.  If

The function returns the error code @code{GPG_ERR_NO_ERROR} if a
log could be queried from the engine, and @code{GPG_ERR_NOT_IMPLEMENTED}
if the log specified in @var{flags} is not available for this engine.
If no log is available @code{GPG_ERR_NO_DATA} is returned.

The value in @var{flags} is a bitwise-or combination of one or
multiple of the following bit values:

@table @code
@item GPGME_AUDITLOG_DIAG
@since{1.11.2}

Obtain diagnostic output which would be written to @code{stderr} in
interactive use of the engine.  This can be used to provide additional
diagnostic information in case of errors in other operations.

Note: If log-file has been set in the configuration the log will
be empty and @code{GPG_ERR_NO_DATA} will be returned.

Implemented for: @code{GPGME_PROTOCOL_OpenPGP}

@item GPGME_AUDITLOG_DEFAULT
@since{1.11.2}

This flag has the value 0 for compatibility reasons.  Obtains additional
information from the engine by issuing the @code{GETAUDITLOG} command.
For @code{GPGME_PROTOCOL_CMS} this provides additional information about
the X509 certificate chain.

Implemented for: @code{GPGME_PROTOCOL_CMS}

@item GPGME_AUDITLOG_HTML
@since{1.1.1}

Same as @code{GPGME_AUDITLOG_DEFAULT} but in HTML.

Implemented for: @code{GPGME_PROTOCOL_CMS}
@end table
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_getauditlog_start @
            (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{output}}, @
            @w{unsigned int @var{flags}})
@since{1.1.1}

This is the asynchronous variant of @code{gpgme_op_getauditlog}.
@end deftypefun

@node Key Management
@section Key Management
@cindex key management

Some of the cryptographic operations require that recipients or
signers are specified.  This is always done by specifying the
respective keys that should be used for the operation.  The following
section describes how such keys can be selected and manipulated.


@menu
* Key objects::                   Description of the key structures.
* Listing Keys::                  Browsing the list of available keys.
* Information About Keys::        Requesting detailed information about keys.
* Manipulating Keys::             Operations on keys.
* Generating Keys::               Creating new key pairs.
* Signing Keys::                  Adding key signatures to public keys.
* Exporting Keys::                Retrieving key data from the key ring.
* Importing Keys::                Adding keys to the key ring.
* Deleting Keys::                 Removing keys from the key ring.
* Changing Passphrases::          Change the passphrase of a key.
* Changing TOFU Data::            Changing data pertaining to TOFU.
* Advanced Key Editing::          Advanced key edit operation.
@end menu

@node Key objects
@subsection Key objects

The keys are represented in GPGME by structures which may only be read
by the application but never be allocated or changed.  They are valid
as long as the key object itself is valid.

@deftp {Data type} gpgme_key_t

The @code{gpgme_key_t} type is a pointer to a key object.  It has the
following members:

@table @code
@item gpgme_keylist_mode_t keylist_mode
@since{0.9.0}

The keylist mode that was active when the key was retrieved.

@item unsigned int revoked : 1
This is true if the key is revoked.

@item unsigned int expired : 1
This is true if the key is expired.

@item unsigned int disabled : 1
This is true if the key is disabled.

@item unsigned int invalid : 1
This is true if the key is invalid. This might have several reasons,
for a example for the S/MIME backend, it will be set during key
listings if the key could not be validated due to missing
certificates or unmatched policies.

@item unsigned int can_encrypt : 1
This is true if the key or one of its subkeys can be used for
encryption and the encryption will likely succeed.

@item unsigned int can_sign : 1
This is true if the key or one of its subkeys can be used to create
data signatures and the signing will likely succeed.

@item unsigned int can_certify : 1
This is true if the key or one of its subkeys can be used to create
key certificates.

@item unsigned int can_authenticate : 1
@since{0.4.5}

This is true if the key (ie one of its subkeys) can be used for
authentication and the authentication will likely succeed.

@item unsigned int has_encrypt : 1
@since{1.23.0}

This is true if the key or one of its subkeys is capable of encryption.
Note that this flag is set even if the key is expired.

@item unsigned int has_sign : 1
@since{1.23.0}

This is true if the key or one of its subkeys is capable of signing.
Note that this flag is set even if the key is expired.

@item unsigned int has_certify : 1
@since{1.23.0}

This is true if the key or one of its subkeys is capable of
certification.  Note that this flag is set even if the key is expired.

@item unsigned int has_authenticate : 1
@since{1.23.0}

This is true if the key or one of its subkeys is capable of
authentication.  Note that this flag is set even if the key is
expired.

@item unsigned int is_qualified : 1
@since{1.1.0}

This is true if the key can be used for qualified signatures according
to local government regulations.

@item unsigned int secret : 1
This is true if the key is a secret key.  Note, that this will always
be true even if the corresponding subkey flag may be false
(offline/stub keys).  This is only set if a listing of secret keys has
been requested or if @code{GPGME_KEYLIST_MODE_WITH_SECRET} is active.

@item unsigned int origin : 5
@since{1.8.0}

Reserved for the origin of this key.

@item gpgme_protocol_t protocol
This is the protocol supported by this key.

@item char *issuer_serial
If @code{protocol} is @code{GPGME_PROTOCOL_CMS}, then this is the
issuer serial.

@item char *issuer_name
If @code{protocol} is @code{GPGME_PROTOCOL_CMS}, then this is the
issuer name.

@item char *chain_id
If @code{protocol} is @code{GPGME_PROTOCOL_CMS}, then this is the
chain ID, which can be used to built the certificate chain.

@item gpgme_validity_t owner_trust
If @code{protocol} is @code{GPGME_PROTOCOL_OpenPGP}, then this is the
owner trust.

@item gpgme_subkey_t subkeys
This is a linked list with the subkeys of the key.  The first subkey
in the list is the primary key and usually available.

@item gpgme_user_id_t uids
This is a linked list with the user IDs of the key.  The first user ID
in the list is the main (or primary) user ID.

@item char *fpr
@since{1.7.0}

This field gives the fingerprint of the primary key.  Note that
this is a copy of the fingerprint of the first subkey.  For an
incomplete key (for example from a verification result) a subkey may
be missing but this field may be set nevertheless.

@item unsigned long last_update
@since{1.8.0}

Reserved for the time of the last update of this key.

@item gpgme_revocation_key_t revkeys
@since{1.24.0}
This is a linked list with the revocation keys for the key.

@end table
@end deftp


@deftp {Data type} gpgme_subkey_t
@since{1.5.0}

The @code{gpgme_subkey_t} type is a pointer to a subkey structure.
Subkeys are one component of a @code{gpgme_key_t} object.  In fact,
subkeys are those parts that contains the real information about the
individual cryptographic keys that belong to the same key object.  One
@code{gpgme_key_t} can contain several subkeys.  The first subkey in
the linked list is also called the primary key.

The subkey structure has the following members:

@table @code
@item gpgme_subkey_t next
This is a pointer to the next subkey structure in the linked list, or
@code{NULL} if this is the last element.

@item unsigned int revoked : 1
This is true if the subkey is revoked.

@item unsigned int expired : 1
This is true if the subkey is expired.

@item unsigned int disabled : 1
This is true if the subkey is disabled.

@item unsigned int invalid : 1
This is true if the subkey is invalid.

@item unsigned int can_encrypt : 1
This is true if the subkey can be used for encryption.

@item unsigned int can_sign : 1
This is true if the subkey can be used to create data signatures.

@item unsigned int can_certify : 1
This is true if the subkey can be used to create key certificates.

@item unsigned int can_authenticate : 1
@since{0.4.5}

This is true if the subkey can be used for authentication.

@item unsigned int is_qualified : 1
@since{1.1.0}

This is true if the subkey can be used for qualified signatures
according to local government regulations.

@item unsigned int is_cardkey : 1

This is true if the secret key or subkey is stored on a smart card.

@item unsigned int is_de_vs : 1
@since{1.8.0}

This is true if the subkey complies with the rules for classified
information in Germany at the restricted level (VS-NfD).  This are
currently RSA keys of at least 3072 bits or ECDH/ECDSA keys using a
Brainpool curve.

@item unsigned int can_renc : 1;
@since {1.20.0}

This is true if the key can be used for restricted encryption (ADSK).

@item unsigned int can_timestamp : 1;
@since {1.20.0}

This is true if the key can be used for timestamping.

@item unsigned int is_group_owned : 1;
@since {1.20.0}

This is true if the private key or subkey is possessed by more than
one person.  Such a key is often called a ``team key''.


@item unsigned int beta_compliance : 1;
@since{1.24.0}
The compliance flags (e.g. is_de_vs) are set but the software has not
yet been approved or is in a beta state.

@item unsigned int subkey_match : 1;
@since{2.0.0}
This flag is set iff the key has been looked up using a
fingerprint with a '!' suffix.

@item unsigned int secret : 1
This is true if the subkey is a secret key.  Note that it will be
false if the key is actually a stub key; i.e., a secret key operation
is currently not possible (offline-key).  This is only set if a
listing of secret keys has been requested or if
@code{GPGME_KEYLIST_MODE_WITH_SECRET} is active.

@item gpgme_pubkey_algo_t pubkey_algo
This is the public key algorithm supported by this subkey.

@item unsigned int length
This is the length of the subkey (in bits).

@item char *keyid
This is the key ID of the subkey in hexadecimal digits.

@item char *fpr
This is the fingerprint of the subkey in hexadecimal digits, if
available.

@item char *v5fpr
For a v4 OpenPGP key this is its v5 style fingerprint of the subkey in
hexadecimal digits, if available.

@item char *keygrip
@since{1.7.0}

The keygrip of the subkey in hex digit form or @code{NULL} if not
available.

@item unsigned long int timestamp
This is the creation timestamp of the subkey.  This is @code{(unsigned
long)(-1)} if the timestamp is invalid, and 0 if it is not available.
Note that an invalid timestamp indicates a bug in the engine.

@item unsigned long int expires
This is the expiration timestamp of the subkey, or 0 if the subkey
does not expire.

@item unsigned int is_cardkey : 1
@since{1.2.0}

True if the secret key is stored on a smart card.

@item char *card_number
@since{1.2.0}

The serial number of a smart card holding this key or @code{NULL}.

@item char *curve
For ECC algorithms the name of the curve.

@end table
@end deftp

@deftp {Data type} gpgme_user_id_t

A user ID is a component of a @code{gpgme_key_t} object.  One key can
have many user IDs.  The first one in the list is the main (or
primary) user ID.

The user ID structure has the following members.

@table @code
@item gpgme_user_id_t next
This is a pointer to the next user ID structure in the linked list, or
@code{NULL} if this is the last element.

@item unsigned int revoked : 1
This is true if the user ID is revoked.

@item unsigned int invalid : 1
This is true if the user ID is invalid.

@item gpgme_validity_t validity
This specifies the validity of the user ID.

@item char *uid
This is the user ID string.

@item char *name
This is the name component of @code{uid}, if available.

@item char *comment
This is the comment component of @code{uid}, if available.

@item char *email
This is the email component of @code{uid}, if available.

@item char *address;
The mail address (addr-spec from RFC-5322) of the user ID string.
This is general the same as the @code{email} part of this structure
but might be slightly different.  If no mail address is available
@code{NULL} is stored.

@item gpgme_tofu_info_t tofu
@since{1.7.0}

If not @code{NULL} information from the TOFU database pertaining to
this user id.

@item gpgme_key_sig_t signatures
This is a linked list with the signatures on this user ID.

@item unsigned int origin : 5
@since{1.8.0}

Reserved for the origin of this user ID.

@item unsigned long last_update
@since{1.8.0}

Reserved for the time of the last update of this user ID.

@item char *uidhash;
A string used by gpg to identify a user ID.  This string can be used
at certain prompts of @code{gpgme_op_edit} to select a user ID.  Users
must be prepared to see a @code{NULL} value here.  The format of the
value is not specified and may depend on the GPGME or GnuPG version.

@end table
@end deftp


@deftp {Data type} gpgme_tofu_info_t

@since{1.7.0}

The @code{gpgme_tofu_info_t} type is a pointer to a tofu info
structure.  Tofu info structures are one component of a
@code{gpgme_user_id_t} object, and provide information from the TOFU
database pertaining to the user ID.

The tofu info structure has the following members:

@table @code
@item gpgme_tofu_info_t next
This is a pointer to the next tofu info structure in the linked
list, or @code{NULL} if this is the last element.

@item unsigned int validity : 3
This is the TOFU validity.  It can have the following values:

@table @code
@item 0
The value @code{0} indicates a conflict.

@item 1
The value @code{1} indicates a key without history.

@item 2
The value @code{2} indicates a key with too little history.

@item 3
The value @code{3} indicates a key with enough history for basic trust.

@item 4
The value @code{4} indicates a key with a lot of history.

@end table

@item unsigned int policy : 4
This is the TOFU policy, see @code{gpgme_tofu_policy_t}.

@item unsigned short signcount
This is the number of signatures seen for this binding (or
@code{USHRT_MAX} if there are more than that).

@item unsigned short encrcount
This is the number of encryptions done with this binding (or
@code{USHRT_MAX} if there are more than that).

@item unsigned long signfirst
Number of seconds since Epoch when the first signature was seen with
this binding.

@item unsigned long signlast
Number of seconds since Epoch when the last signature was seen with
this binding.

@item unsigned long encrfirst
Number of seconds since Epoch when the first encryption was done with
this binding.

@item unsigned long encrlast
Number of seconds since Epoch when the last encryption was done with
this binding.

@item char *description
A human-readable string summarizing the TOFU data (or NULL).

@end table
@end deftp


@deftp {Data type} gpgme_key_sig_t

The @code{gpgme_key_sig_t} type is a pointer to a key signature structure.
Key signatures are one component of a @code{gpgme_key_t} object, and
validate user IDs on the key in the OpenPGP protocol.

The signatures on a key are only available if the key was retrieved
via a listing operation with the @code{GPGME_KEYLIST_MODE_SIGS} mode
enabled, because it can be expensive to retrieve all signatures of a
key.

The signature notations on a key signature are only available if the
key was retrieved via a listing operation with the
@code{GPGME_KEYLIST_MODE_SIG_NOTATIONS} mode enabled, because it can
be expensive to retrieve all signature notations.

The key signature structure has the following members:

@table @code
@item gpgme_key_sig_t next
This is a pointer to the next key signature structure in the linked
list, or @code{NULL} if this is the last element.

@item unsigned int revoked : 1
This is true if the key signature is a revocation signature.

@item unsigned int expired : 1
This is true if the key signature is expired.

@item unsigned int invalid : 1
This is true if the key signature is invalid.

@item unsigned int exportable : 1
This is true if the key signature is exportable.

@item unsigned int trust_depth : 8
This is the depth of a trust signature, or 0 if the key signature is not
a trust signature.

@item unsigned int trust_value : 8
This is the trust amount of a trust signature.

@item gpgme_pubkey_algo_t pubkey_algo
This is the public key algorithm used to create the signature.

@item char *keyid
This is the key ID of the key (in hexadecimal digits) used to create
the signature.

@item unsigned long int timestamp
This is the creation timestamp of the key signature.  This is
@code{(unsigned long)(-1)} if the timestamp is invalid, and 0 if it is
not available.

@item unsigned long int expires
This is the expiration timestamp of the key signature, or 0 if the key
signature does not expire.

@item char *trust_scope
This is a regular expression that limits the scope of a trust signature.
Users must be prepared to see a @code{NULL} value here.

@item gpgme_error_t status
This is the status of the signature and has the same meaning as the
member of the same name in a @code{gpgme_signature_t} object.

@item unsigned int sig_class
This specifies the signature class of the key signature.  The meaning
is specific to the crypto engine.

@item char *uid
This is the main user ID of the key used to create the signature.

@item char *name
This is the name component of @code{uid}, if available.

@item char *comment
This is the comment component of @code{uid}, if available.

@item char *email
This is the email component of @code{uid}, if available.

@item gpgme_sig_notation_t notations
This is a linked list with the notation data and policy URLs.
@end table
@end deftp


@deftp {Data type} gpgme_revocation_key_t
@since{1.24.0}

The @code{gpgme_revocation_key_t} type is a pointer to a revocation key
structure.  Revocation key structures are one component of a
@code{gpgme_key_t} object.  They provide information about the designated
revocation keys for a key.

The revocation key structure has the following members:

@table @code
@item gpgme_revocation_key_t next
This is a pointer to the next revocation key structure in the linked list,
or @code{NULL} if this is the last element.

@item gpgme_pubkey_algo_t pubkey_algo
This is the public key algorithm of the revocation key.

@item char *fpr
This is the fingerprint of the revocation_key in hexadecimal digits.

@item unsigned int key_class
This is the class of the revocation key signature subpacket.

@item unsigned int sensitive : 1
This is true if the revocation key is marked as sensitive.

@end table
@end deftp



@node Listing Keys
@subsection Listing Keys
@cindex listing keys
@cindex key listing
@cindex key listing, start
@cindex key ring, list
@cindex key ring, search

@deftypefun gpgme_error_t gpgme_op_keylist_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}}, @w{int @var{secret_only}})

The function @code{gpgme_op_keylist_start} initiates a key listing
operation inside the context @var{ctx}.  It sets everything up so that
subsequent invocations of @code{gpgme_op_keylist_next} return the keys
in the list.

If @var{pattern} is @code{NULL}, all available keys are returned.
Otherwise, @var{pattern} contains an engine specific expression that
is used to limit the list to all keys matching the pattern.  Note that
the total length of the pattern is restricted to an engine-specific
maximum (a couple of hundred characters are usually accepted).  The
pattern should be used to restrict the search to a certain common name
or user, not to list many specific keys at once by listing their
fingerprints or key IDs.

If @var{secret_only} is not @code{0}, the list is restricted to secret
keys only.

The context will be busy until either all keys are received (and
@code{gpgme_op_keylist_next} returns @code{GPG_ERR_EOF}), or
@code{gpgme_op_keylist_end} is called to finish the operation.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_keylist_ext_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}[]}, @w{int @var{secret_only}}, @w{int @var{reserved}})

The function @code{gpgme_op_keylist_ext_start} initiates an extended
key listing operation inside the context @var{ctx}.  It sets
everything up so that subsequent invocations of
@code{gpgme_op_keylist_next} return the keys in the list.

If @var{pattern} or @var{*pattern} is @code{NULL}, all available keys
are returned.  Otherwise, @var{pattern} is a @code{NULL} terminated
array of strings that are used to limit the list to all keys matching
at least one of the patterns verbatim.  Note that the total length of
all patterns is restricted to an engine-specific maximum (the exact
limit also depends on the number of patterns and amount of quoting
required, but a couple of hundred characters are usually accepted).
Patterns should be used to restrict the search to a certain common
name or user, not to list many specific keys at once by listing their
fingerprints or key IDs.

If @var{secret_only} is not @code{0}, the list is restricted to secret
keys only.

The value of @var{reserved} must be @code{0}.

The context will be busy until either all keys are received (and
@code{gpgme_op_keylist_next} returns @code{GPG_ERR_EOF}), or
@code{gpgme_op_keylist_end} is called to finish the operation.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_keylist_from_data_start @
            (@w{gpgme_ctx_t @var{ctx}}, @
             @w{gpgme_data_t @var{data}}, @
             @w{int @var{reserved}})

@since{1.8.0}

The function @code{gpgme_op_keylist_from_data_start} initiates a key
listing operation inside the context @var{ctx}.  In contrast to the
other key listing operation the keys are read from the supplied
@var{data} and not from the local key database.  The keys are also not
imported into the local key database.  The function sets everything up
so that subsequent invocations of @code{gpgme_op_keylist_next} return
the keys from @var{data}.

The value of @var{reserved} must be @code{0}.

This function requires at least GnuPG version 2.1.14 and currently
works only with OpenPGP keys.

The context will be busy until either all keys are received (and
@code{gpgme_op_keylist_next} returns @code{GPG_ERR_EOF}), or
@code{gpgme_op_keylist_end} is called to finish the operation.
While the context is busy @var{data} may not be released.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_keylist_next (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t *@var{r_key}})

The function @code{gpgme_op_keylist_next} returns the next key in the
list created by a previous @code{gpgme_op_keylist_start} operation in
the context @var{ctx}.  The key will have one reference for the user.
@xref{Manipulating Keys}.

This is the only way to get at @code{gpgme_key_t} objects in
@acronym{GPGME}.

If the last key in the list has already been returned,
@code{gpgme_op_keylist_next} returns @code{GPG_ERR_EOF}.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{r_key} is not a valid pointer, and
@code{GPG_ERR_ENOMEM} if there is not enough memory for the operation.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_keylist_end (@w{gpgme_ctx_t @var{ctx}})

The function @code{gpgme_op_keylist_end} ends a pending key list
operation in the context @var{ctx}.

After the operation completed successfully, the result of the key
listing operation can be retrieved with
@code{gpgme_op_keylist_result}.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and @code{GPG_ERR_ENOMEM} if at some
time during the operation there was not enough memory available.
@end deftypefun

The following example illustrates how all keys containing a certain
string (@code{g10code}) can be listed with their key ID and the name
and email address of the main user ID:

@example
gpgme_ctx_t ctx;
gpgme_key_t key;
gpgme_error_t err = gpgme_new (&ctx);

if (!err)
  @{
    err = gpgme_op_keylist_start (ctx, "g10code", 0);
    while (!err)
      @{
        err = gpgme_op_keylist_next (ctx, &key);
        if (err)
          break;
        printf ("%s:", key->subkeys->keyid);
        if (key->uids && key->uids->name)
          printf (" %s", key->uids->name);
        if (key->uids && key->uids->email)
          printf (" <%s>", key->uids->email);
        putchar ('\n');
        gpgme_key_release (key);
      @}
    gpgme_release (ctx);
  @}
if (gpg_err_code (err) != GPG_ERR_EOF)
  @{
    fprintf (stderr, "can not list keys: %s\n", gpgme_strerror (err));
    exit (1);
  @}
@end example

@deftp {Data type} {gpgme_keylist_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_keylist_*} operation.  After successfully ending a key
listing operation, you can retrieve the pointer to the result with
@code{gpgme_op_keylist_result}.  The structure contains the following
member:

@table @code
@item unsigned int truncated : 1
This is true if the crypto backend had to truncate the result, and
less than the desired keys could be listed.
@end table
@end deftp

@deftypefun gpgme_keylist_result_t gpgme_op_keylist_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_keylist_result} returns a
@code{gpgme_keylist_result_t} pointer to a structure holding the
result of a @code{gpgme_op_keylist_*} operation.  The pointer is only
valid if the last operation on the context was a key listing
operation, and if this operation finished successfully.  The returned
pointer is only valid until the next operation is started on the
context.
@end deftypefun

In a simple program, for which a blocking operation is acceptable, the
following function can be used to retrieve a single key.

@deftypefun gpgme_error_t gpgme_get_key (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{fpr}}, @w{gpgme_key_t *@var{r_key}}, @w{int @var{secret}})
The function @code{gpgme_get_key} gets the key with the fingerprint
(or key ID) @var{fpr} from the crypto backend and return it in
@var{r_key}.  If @var{secret} is true, get the secret key.  The
currently active keylist mode is used to retrieve the key.  The key
will have one reference for the user.

If the key is not found in the keyring, @code{gpgme_get_key} returns
the error code @code{GPG_ERR_EOF} and *@var{r_key} will be set to
@code{NULL}.

The function returns the error code @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{r_key} is not a valid pointer or @var{fpr} is not a
fingerprint or key ID, @code{GPG_ERR_AMBIGUOUS_NAME} if the key ID was
not a unique specifier for a key, and @code{GPG_ERR_ENOMEM} if at some
time during the operation there was not enough memory available.
@end deftypefun


@node Information About Keys
@subsection Information About Keys
@cindex key, information about
@cindex key, attributes
@cindex attributes, of a key

Please see the beginning of this section for more information about
@code{gpgme_key_t} objects.

@deftp {Data type} gpgme_validity_t
The @code{gpgme_validity_t} type is used to specify the validity of a user ID
in a key.  The following validities are defined:

@table @code
@item GPGME_VALIDITY_UNKNOWN
The user ID is of unknown validity.  The string representation of this
validity is ``?''.

@item GPGME_VALIDITY_UNDEFINED
The validity of the user ID is undefined.  The string representation of this
validity is ``q''.

@item GPGME_VALIDITY_NEVER
The user ID is never valid.  The string representation of this
validity is ``n''.

@item GPGME_VALIDITY_MARGINAL
The user ID is marginally valid.  The string representation of this
validity is ``m''.

@item GPGME_VALIDITY_FULL
The user ID is fully valid.  The string representation of this
validity is ``f''.

@item GPGME_VALIDITY_ULTIMATE
The user ID is ultimately valid.  The string representation of this
validity is ``u''.
@end table
@end deftp




@node Manipulating Keys
@subsection Manipulating Keys
@cindex key, manipulation

@deftypefun void gpgme_key_ref (@w{gpgme_key_t @var{key}})
The function @code{gpgme_key_ref} acquires an additional reference for
the key @var{key}.
@end deftypefun

@deftypefun void gpgme_key_unref (@w{gpgme_key_t @var{key}})
The function @code{gpgme_key_unref} releases a reference for the key
@var{key}.  If this was the last reference, the key will be destroyed
and all resources associated to it will be released.
@end deftypefun

@c
@c  gpgme_op_setexpire
@c
@deftypefun gpgme_error_t gpgme_op_setexpire @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{unsigned long @var{expires}}, @
       @w{const char *@var{subfprs}}, @
       @w{unsigned int @var{reserved}});

@since{1.14.1}

The function @code{gpgme_op_setexpire} sets the expiration time of
the key @var{key} or of the specified subkeys.
This function requires at least version 2.1.22 of GnuPG.

@var{key} specifies the key to operate on.

@var{expires} specifies the expiration time in seconds from now.
To be similar to other usages where expiration times are provided
in unsigned long this is similar to the key creation date
and so it is in seconds from NOW.

The common case is to use 0 to not set an expiration time.
Note that this parameter takes an unsigned long value and not
a @code{time_t} to avoid problems on systems which use a signed
32 bit @code{time_t}.  Note further that the OpenPGP protocol
uses 32 bit values for timestamps and thus can only encode dates
up to the year 2106.

@var{subfprs} selects the subkey(s) for which the expiration time
should be set.  If @var{subfprs} is set to @code{NULL}, then the
expiration time of the primary key is set.  If @var{subfprs} is
an asterisk (@code{*}), then the expiration times of all non-revoked
and not yet expired subkeys are set.  To select more than one subkey
put all subkey fingerprints into one string separated by linefeeds
characters (@code{\n}).

@var{reserved} is reserved for later use and must be @code{0}.

@end deftypefun


@deftypefun gpgme_error_t gpgme_op_setexpire_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{unsigned long @var{expires}}, @
       @w{const char *@var{subfprs}}, @
       @w{unsigned int @var{flags}});

@since{1.14.1}

The function @code{gpgme_op_setexpire_start} initiates a
@code{gpgme_op_setexpire} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun

@c
@c  gpgme_op_setownertrust
@c
@deftypefun gpgme_error_t gpgme_op_setownertrust @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{value}});

@since{1.24.0}

The function @code{gpgme_op_setownertrust} sets the owner trust of the
key @var{key} or it sets the disable flag of the key @var{key}.
This function only works for OpenPGP and requires at least version 2.4.6
of GnuPG.

@var{key} specifies the key to operate on.

@var{value} specifies the owner trust value to set.  Valid values are
"undefined", "never", "marginal", "full", "ultimate".  If @var{value} is
the string "disable" then the key @var{key} is disabled.  If @var{value}
is the string "enable" then the key @var{key} is re-enabled.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation was completed successfully, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, and @code{GPG_ERR_INV_VALUE} if
@var{key} is not a valid pointer or not a valid key or if @var{value} is
not a valid pointer or the empty string.

@end deftypefun


@deftypefun gpgme_error_t gpgme_op_setownertrust_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{value}});

@since{1.24.0}

The function @code{gpgme_op_setownertrust_start} initiates a
@code{gpgme_op_setownertrust} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

The function returns the same error codes as @code{gpgme_op_setownertrust}.

@end deftypefun


@node Generating Keys
@subsection Generating Keys
@cindex key, creation
@cindex key ring, add

GPGME provides a set of functions to create public key pairs.  Most of
these functions require the use of GnuPG 2.1 and later; for older
GnuPG versions the @code{gpgme_op_genkey} function can be used.
Existing code which wants to update to the new functions or new code
which shall supports older GnuPG versions may try the new functions
first and provide a fallback to the old function if the error code
@code{GPG_ERR_NOT_SUPPORTED} is received.

@c
@c  gpgme_op_createkey
@c
@deftypefun gpgme_error_t gpgme_op_createkey @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{const char *@var{userid}}, @
       @w{const char *@var{algo}}, @
       @w{unsigned long @var{reserved}}, @
       @w{unsigned long @var{expires}}, @
       @w{gpgme_key_t @var{extrakey}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_createkey} generates a new key for the
procotol active in the context @var{ctx}.  As of now this function
does only work for OpenPGP and requires at least version 2.1.13 of
GnuPG.

@var{userid} is commonly the mail address associated with the key.
GPGME does not require a specific syntax but if more than a mail
address is given, RFC-822 style format is suggested.  The value is
expected to be in UTF-8 encoding (i.e., no IDN encoding for mail
addresses).  This is a required parameter.

@var{algo} specifies the algorithm for the new key (actually a keypair
of public and private key).  For a list of supported algorithms, see
the GnuPG manual.  If @var{algo} is @code{NULL} or the string
"default", the key is generated using the default algorithm of the
engine.  If the string "future-default" is used the engine may use an
algorithm which is planned to be the default in a future release of
the engine; however existing implementation of the protocol may not be
able to already handle such future algorithms.  For the OpenPGP
protocol, the specification of a default algorithm, without requesting
a non-default usage via @var{flags}, triggers the creation of a
primary key plus a secondary key (subkey).

@var{reserved} must be set to zero.

@var{expires} specifies the expiration time in seconds.  If you supply
0, a reasonable expiration time is chosen.  Use the flag
@code{GPGME_CREATE_NOEXPIRE} to create keys that do not expire.  Note
that this parameter takes an unsigned long value and not a
@code{time_t} to avoid problems on systems which use a signed 32 bit
@code{time_t}.  Note further that the OpenPGP protocol uses 32 bit
values for timestamps and thus can only encode dates up to the year
2106.

@var{extrakey} is currently not used and must be set to @code{NULL}.
A future version of GPGME may use this parameter to create X.509 keys.

@var{flags} can be set to the bit-wise OR of the following flags:

@table @code
@item GPGME_CREATE_SIGN
@itemx GPGME_CREATE_ENCR
@itemx GPGME_CREATE_CERT
@itemx GPGME_CREATE_AUTH
@since{1.7.0}

Do not create the key with the default capabilities (key usage) of the
requested algorithm but use those explicitly given by these flags:
``signing'', ``encryption'', ``certification'', or ``authentication''.
The allowed combinations depend on the algorithm.

If any of these flags are set and a default algorithm has been
selected only one key is created in the case of the OpenPGP
protocol.

@item GPGME_CREATE_NOPASSWD
@since{1.7.0}

Request generation of the key without password protection.

@item GPGME_CREATE_SELFSIGNED
@since{1.7.0}

For an X.509 key do not create a CSR but a self-signed certificate.
This has not yet been implemented.

@item GPGME_CREATE_NOSTORE
@since{1.7.0}

Do not store the created key in the local key database.
This has not yet been implemented.

@item GPGME_CREATE_WANTPUB
@itemx GPGME_CREATE_WANTSEC
@since{1.7.0}

Return the public or secret key as part of the result structure.
This has not yet been implemented.

@item GPGME_CREATE_FORCE
@since{1.7.0}

The engine does not allow the creation of a key with a user ID
already existing in the local key database.  This flag can be used to
override this check.

@item GPGME_CREATE_NOEXPIRE
@since{1.9.0}

Request generation of keys that do not expire.

@item GPGME_CREATE_ADSK
@since{1.24.0}

Add an ADSK to the key.  With this flag @var{algo} is expected to be
the hexified fingerprint of the ADSK to be added; this must be a
subkey.  If the string "default" is used for @var{algo} the engine
will add all ADSK as it would do for new keys.

@item GPGME_CREATE_GROUP
@since{2.0.0}

Set the ``group owned'' flag for the new generated key or subkey.

@end table

After the operation completed successfully, information about the
created key can be retrieved with @code{gpgme_op_genkey_result}.

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun


@deftypefun gpgme_error_t gpgme_op_createkey_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{const char *@var{userid}}, @
       @w{const char *@var{algo}}, @
       @w{unsigned long @var{reserved}}, @
       @w{unsigned long @var{expires}}, @
       @w{gpgme_key_t @var{extrakey}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_createkey_start} initiates a
@code{gpgme_op_createkey} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun

@c
@c  gpgme_op_createsubkey
@c
@deftypefun gpgme_error_t gpgme_op_createsubkey @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{algo}}, @
       @w{unsigned long @var{reserved}}, @
       @w{unsigned long @var{expires}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_createsubkey} creates and adds a new
subkey to the primary OpenPGP key given by @var{KEY}.  The only
allowed protocol in @var{ctx} is @code{GPGME_PROTOCOL_OPENPGP}.
Subkeys (aka secondary keys) are a concept in the OpenPGP protocol to
bind several keys to a primary key.  As of now this function requires
at least version 2.1.13 of GnuPG.

@var{key} specifies the key to operate on.

@var{algo} specifies the algorithm for the new subkey.  For a list of
supported algorithms, see the GnuPG manual.  If @var{algo} is
@code{NULL} or the string "default", the subkey is generated using the
default algorithm for an encryption subkey of the engine.  If the
string "future-default" is used the engine may use an encryption
algorithm which is planned to be the default in a future release of
the engine; however existing implementation of the protocol may not be
able to already handle such future algorithms.

@var{reserved} must be set to zero.

@var{expires} specifies the expiration time in seconds.  If you supply
0, a reasonable expiration time is chosen.  Use the flag
@code{GPGME_CREATE_NOEXPIRE} to create keys that do not expire.  Note
that this parameter takes an unsigned long value and not a
@code{time_t} to avoid problems on systems which use a signed 32 bit
@code{time_t}.  Note further that the OpenPGP protocol uses 32 bit
values for timestamps and thus can only encode dates up to the year
2106.

@var{flags} takes the same values as described above for
@code{gpgme_op_createkey}.

If the @code{GPGME_CREATE_ADSK} flag is set, the subkey fingerprint
given in the @code{algo} parameter is added as an ADSK
to the key.

After the operation completed successfully, information about the
created key can be retrieved with @code{gpgme_op_genkey_result}.

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.


@end deftypefun

@deftypefun gpgme_error_t gpgme_op_createsubkey_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{algo}}, @
       @w{unsigned long @var{reserved}}, @
       @w{unsigned long @var{expires}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_createsubkey_start} initiates a
@code{gpgme_op_createsubkey} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun


@c
@c  gpgme_op_adduid
@c
@deftypefun gpgme_error_t gpgme_op_adduid @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_adduid} adds a new user ID to the OpenPGP
key given by @var{KEY}.  Adding additional user IDs after key creation
is a feature of the OpenPGP protocol and thus the protocol for the
context @var{ctx} must be set to OpenPGP.  As of now this function
requires at least version 2.1.13 of GnuPG.

@var{key} specifies the key to operate on.

@var{userid} is the user ID to add to the key.  A user ID is commonly
the mail address to be associated with the key.  GPGME does not
require a specific syntax but if more than a mail address is given,
RFC-822 style format is suggested.  The value is expected to be in
UTF-8 encoding (i.e., no IDN encoding for mail addresses).  This is a
required parameter.

@var{flags} are currently not used and must be set to zero.

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun

@deftypefun gpgme_error_t gpgme_op_adduid_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_adduid_start} initiates a
@code{gpgme_op_adduid} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun


@c
@c  gpgme_op_revuid
@c
@deftypefun gpgme_error_t gpgme_op_revuid @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_revuid} revokes a user ID from the OpenPGP
key given by @var{KEY}.  Revoking user IDs after key creation is a
feature of the OpenPGP protocol and thus the protocol for the context
@var{ctx} must be set to OpenPGP.  As of now this function requires at
least version 2.1.13 of GnuPG.

@var{key} specifies the key to operate on.

@var{userid} is the user ID to be revoked from the key.  The user ID
must be given verbatim because the engine does an exact and case
sensitive match.  Thus the @code{uid} field from the user ID object
(@code{gpgme_user_id_t}) is to be used.  This is a required parameter.

@var{flags} are currently not used and must be set to zero.

Note that the engine won't allow to revoke the last valid user ID.  To
change a user ID is better to first add the new user ID, then revoke
the old one, and finally publish the key.

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun

@deftypefun gpgme_error_t gpgme_op_revuid_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_revuid_start} initiates a
@code{gpgme_op_revuid} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun


@c
@c  gpgme_op_set_uid_flag
@c
@deftypefun gpgme_error_t gpgme_op_set_ui_flag @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{cons char * @var{name}}, @
       @w{cons char * @var{value}});

@since{1.8.0}

The function @code{gpgme_op_set_uid_flag} is used to set flags on a
user ID from the OpenPGP key given by @var{KEY}.  Setting flags on
user IDs after key creation is a feature of the OpenPGP protocol and
thus the protocol for the context @var{ctx} must be set to OpenPGP.

@var{key} specifies the key to operate on.  This parameters is required.

@var{userid} is the user ID of the key to be manipulated.  This user ID
must be given verbatim because the engine does an exact and case
sensitive match.  Thus the @code{uid} field from the user ID object
(@code{gpgme_user_id_t}) is to be used.  This is a required parameter.

@var{name} names the flag which is to be changed.  The only currently
supported flag is:

@table @code
@item primary
This sets the primary key flag on the given user ID.  All other
primary key flag on other user IDs are removed.  @var{value} must be
given as NULL.  For technical reasons this functions bumps the
creation timestamp of all affected self-signatures up by one second.
At least GnuPG version 2.1.20 is required.

@end table

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun

@deftypefun gpgme_error_t gpgme_op_set_uid_flag_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{cons char * @var{name}}, @
       @w{cons char * @var{value}});

@since{1.8.0}

The function @code{gpgme_op_set_uid_flag_start} initiates a
@code{gpgme_op_set_uid_flag} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun

@c
@c  gpgme_op_genkey
@c
@deftypefun gpgme_error_t gpgme_op_genkey @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{const char *@var{parms}}, @
       @w{gpgme_data_t @var{public}}, @
       @w{gpgme_data_t @var{secret}})

The function @code{gpgme_op_genkey} generates a new key pair in the
context @var{ctx}.  The meaning of @var{public} and @var{secret}
depends on the crypto backend.

GPG does not support @var{public} and @var{secret}, they should be
@code{NULL}.  GnuPG will generate a key pair and add it to the
standard key ring.  The fingerprint of the generated key is available
with @code{gpgme_op_genkey_result}.

GpgSM requires @var{public} to be a writable data object.  GpgSM will
generate a secret key (which will be stored by @command{gpg-agent},
and return a certificate request in @var{public}, which then needs to
be signed by the certification authority and imported before it can be
used.  GpgSM does not make the fingerprint available.

The argument @var{parms} specifies parameters for the key in an string
that looks something like XML.  The details about the format of
@var{parms} are specific to the crypto engine used by @var{ctx}.  The
first line of the parameters must be @code{<GnupgKeyParams
format="internal">} and the last line must be
@code{</GnupgKeyParams>}.  Every line in between the first and last
lines is treated as a Header: Value pair.  In particular, no XML
escaping is necessary if you need to include the characters @code{<},
@code{>}, or @code{&}.

Here is an example for GnuPG as the crypto engine (all parameters of
OpenPGP key generation are documented in the GPG manual):

@example
<GnupgKeyParms format="internal">
Key-Type: default
Subkey-Type: default
Name-Real: Joe Tester
Name-Comment: with stupid passphrase
Name-Email: joe@@foo.bar
Expire-Date: 0
Passphrase: abc
</GnupgKeyParms>
@end example

Here is an example for GpgSM as the crypto engine (all parameters of
OpenPGP key generation are documented in the GPGSM manual):

@example
<GnupgKeyParms format="internal">
Key-Type: RSA
Key-Length: 1024
Name-DN: C=de,O=g10 code,OU=Testlab,CN=Joe 2 Tester
Name-Email: joe@@foo.bar
</GnupgKeyParms>
@end example

Strings should be given in UTF-8 encoding.  The only format supported
for now is ``internal''.  The content of the @code{GnupgKeyParms}
container is passed verbatim to the crypto backend.  Control
statements are not allowed.

After the operation completed successfully, the result can be
retrieved with @code{gpgme_op_genkey_result}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{parms} is not a well-formed string (e.g., does not have the
expected tag-like headers and footers), @code{GPG_ERR_NOT_SUPPORTED}
if @var{public} or @var{secret} is not valid, and
@code{GPG_ERR_GENERAL} if no key was created by the backend.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_genkey_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{parms}}, @w{gpgme_data_t @var{public}}, @w{gpgme_data_t @var{secret}})

The function @code{gpgme_op_genkey_start} initiates a
@code{gpgme_op_genkey} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{parms} is not a valid XML string, and
@code{GPG_ERR_NOT_SUPPORTED} if @var{public} or @var{secret} is not
@code{NULL}.
@end deftypefun


@c
@c  gpgme_op_genkey_result
@c
@deftp {Data type} {gpgme_genkey_result_t}

This is a pointer to a structure used to store the result of a
@code{gpgme_op_genkey} operation.  After successfully generating a
key, you can retrieve the pointer to the result with
@code{gpgme_op_genkey_result}.  The structure contains the following
members:

@table @code
@item unsigned int primary : 1
This flag is set to 1 if a primary key was created and to 0
if not.

@item unsigned int sub : 1
This flag is set to 1 if a subkey was created and to 0 if not.

@item unsigned int uid : 1
This flag is set to 1 if a user ID was created and to 0 if not.

@item char *fpr
This is the fingerprint of the key that was created.  If both a
primary and a subkey were generated, the fingerprint of the primary
key will be returned.  If the crypto engine does not provide the
fingerprint, @code{fpr} will be a null pointer.

@item gpgme_data_t pubkey
@since{1.7.0}

This will eventually be used to return the public key.  It is
currently not used.

@item gpgme_data_t seckey
@since{1.7.0}

This will eventually be used to return the secret key.  It is
currently not used.

@end table
@end deftp

@deftypefun gpgme_genkey_result_t gpgme_op_genkey_result (@w{gpgme_ctx_t @var{ctx}})

The function @code{gpgme_op_genkey_result} returns a
@code{gpgme_genkey_result_t} pointer to a structure holding the result of
a @code{gpgme_op_genkey} operation.  The pointer is only valid if the
last operation on the context was a @code{gpgme_op_genkey} or
@code{gpgme_op_genkey_start} operation, and if this operation finished
successfully.  The returned pointer is only valid until the next
operation is started on the context.

@end deftypefun


@c
@c  SIGNING KEYS
@c
@node Signing Keys
@subsection Signing Keys
@cindex key, signing

Key signatures are a unique concept of the OpenPGP protocol.  They can
be used to certify the validity of a key and are used to create the
Web-of-Trust (WoT).  Instead of using the @code{gpgme_op_interact}
function along with a finite state machine, GPGME provides a
convenient function to create key signatures when using modern GnuPG
versions.


@c
@c  gpgme_op_keysign
@c
@deftypefun gpgme_error_t gpgme_op_keysign @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned long @var{expires}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_keysign} adds a new key signature to the
public key @var{KEY}.   This function requires at least version 2.1.12 of
GnuPG.

@var{CTX} is the usual context which describes the protocol to use
(which must be OpenPGP) and has also the list of signer keys to be
used for the signature.  The common case is to use the default key for
signing other keys.  If another key or more than one key shall be used
for a key signature, @code{gpgme_signers_add} can be used.
@xref{Selecting Signers}.

@var{key} specifies the key to operate on.

@var{userid} selects the user ID or user IDs to be signed.  If
@var{userid} is set to @code{NULL} all valid user IDs are signed.  The
user ID must be given verbatim because the engine does an exact and
case sensitive match.  Thus the @code{uid} field from the user ID
object (@code{gpgme_user_id_t}) is to be used.  To select more than
one user ID put them all into one string separated by linefeeds
characters (@code{\n}) and set the flag @code{GPGME_KEYSIGN_LFSEP}.

@var{expires} specifies the expiration time of the new signature in
seconds.  The common case is to use 0 to not set an expiration date.
However, if the configuration of the engine defines a default
expiration for key signatures, that is still used unless the flag
@code{GPGME_KEYSIGN_NOEXPIRE} is used.  Note that this parameter takes
an unsigned long value and not a @code{time_t} to avoid problems on
systems which use a signed 32 bit @code{time_t}.  Note further that
the OpenPGP protocol uses 32 bit values for timestamps and thus can
only encode dates up to the year 2106.

@var{flags} can be set to the bit-wise OR of the following flags:

@table @code
@item GPGME_KEYSIGN_LOCAL
@since{1.7.0}

Instead of creating an exportable key signature, create a key
signature which is is marked as non-exportable.

@item GPGME_KEYSIGN_LFSEP
@since{1.7.0}

Although linefeeds are uncommon in user IDs this flag is required to
explicitly declare that @var{userid} may contain several linefeed
separated user IDs.

@item GPGME_KEYSIGN_NOEXPIRE
Force the creation of a key signature without an expiration date.  This
overrides @var{expire} and any local configuration of the engine.

@item GPGME_KEYSIGN_FORCE
Force the creation of a new signature even if one already exists.
This flag has an effect only if the gpg version is at least 2.2.28 but
won't return an error with older versions.

@end table

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun


@deftypefun gpgme_error_t gpgme_op_keysign_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned long @var{expires}}, @
       @w{unsigned int @var{flags}});

@since{1.7.0}

The function @code{gpgme_op_keysign_start} initiates a
@code{gpgme_op_keysign} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun


@c
@c  gpgme_op_revsig
@c
@deftypefun gpgme_error_t gpgme_op_revsig @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{gpgme_key_t @var{signing_key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.14.1}

The function @code{gpgme_op_revsig} revokes key signatures of the
public key @var{key} made with the key @var{signing_key}.   This
function requires at least version 2.2.24 of GnuPG.

@var{key} specifies the key to operate on.

@var{signing_key} specifies the key whose signatures shall be revoked.

@var{userid} selects the user ID or user IDs whose signatures shall
be revoked.  If @var{userid} is set to @code{NULL} the signatures
on all user IDs are revoked.  The user ID must be given verbatim
because the engine does an exact and case sensitive match.  Thus the
@code{uid} field from the user ID object (@code{gpgme_user_id_t}) is to
be used.  To select more than one user ID put them all into one string
separated by linefeeds characters (@code{\n}) and set the flag
@code{GPGME_REVSIG_LFSEP}.

@var{flags} can be set to the bit-wise OR of the following flags:

@table @code
@item GPGME_REVSIG_LFSEP
@since{1.14.1}

Although linefeeds are uncommon in user IDs this flag is required to
explicitly declare that @var{userid} may contain several linefeed
separated user IDs.

@end table

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun


@deftypefun gpgme_error_t gpgme_op_revsig_start @
      (@w{gpgme_ctx_t @var{ctx}}, @
       @w{gpgme_key_t @var{key}}, @
       @w{gpgme_key_t @var{signing_key}}, @
       @w{const char *@var{userid}}, @
       @w{unsigned int @var{flags}});

@since{1.14.1}

The function @code{gpgme_op_revsig_start} initiates a
@code{gpgme_op_revsig} operation; see there for details.  It must
be completed by calling @code{gpgme_wait} on the context.
@xref{Waiting For Completion}.

@end deftypefun


@c
@c  EXPORTING KEYS
@c
@node Exporting Keys
@subsection Exporting Keys
@cindex key, export
@cindex key ring, export from

Exporting keys means the same as running @command{gpg} with the command
@option{--export}.  However, a mode flag can be used to change the way
the export works.  The available mode flags are described below, they
may be or-ed together.

@table @code

@item GPGME_EXPORT_MODE_EXTERN
If this bit is set, the output is send directly to the default
keyserver. This is currently only allowed for OpenPGP keys.  It is good
practise to not send more than a few dozens key to a keyserver at one
time.  Using this flag requires that the @var{keydata} argument of the
export function is set to @code{NULL}.

@item GPGME_EXPORT_MODE_MINIMAL
@since{1.3.1}

If this bit is set, the smallest possible key is exported.  For OpenPGP
keys it removes all signatures except for the latest self-signatures.
For X.509 keys it has no effect.

@item GPGME_EXPORT_MODE_SSH
@since{1.4.0}

If this bit is set, the latest authentication key of the requested
OpenPGP key is exported in the OpenSSH public key format.  This
accepts just a single key; to force the export of a specific subkey
a fingerprint pattern with an appended exclamation mark may be used.

@item GPGME_EXPORT_MODE_SECRET
@since{1.6.0}

Instead of exporting the public key, the secret key is exported.  This
may not be combined with @code{GPGME_EXPORT_MODE_EXTERN}.  For X.509
the export format is PKCS#8.

@item GPGME_EXPORT_MODE_SECRET_SUBKEY
@since{1.17.0}

If this bit is set, then a secret subkey is exported.  The subkey to
export must be specified with fingerprint pattern with an appended
exclamation mark.  This is currently only allowed for OpenPGP keys.
This flag may not be combined with @code{GPGME_EXPORT_MODE_EXTERN}.
This flag is not supported by the export functions that take an array
of keys.

@item GPGME_EXPORT_MODE_RAW
@since{1.6.0}

If this flag is used with @code{GPGME_EXPORT_MODE_SECRET} for an X.509
key the export format will be changed to PKCS#1.  This flag may not be
used with OpenPGP.

@item GPGME_EXPORT_MODE_PKCS12
@since{1.6.0}

If this flag is used with @code{GPGME_EXPORT_MODE_SECRET} for an X.509
key the export format will be changed to PKCS#12 which also includes
the certificate.  This flag may not be used with OpenPGP.

@end table



@deftypefun gpgme_error_t gpgme_op_export (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_export} extracts public keys and returns
them in the data buffer @var{keydata}.  The output format of the key
data returned is determined by the @acronym{ASCII} armor attribute set
for the context @var{ctx}, or, if that is not set, by the encoding
specified for @var{keydata}.

If @var{pattern} is @code{NULL}, all available keys are returned.
Otherwise, @var{pattern} contains an engine specific expression that
is used to limit the list to all keys matching the pattern.

@var{mode} is usually 0; other values are described above.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{keydata} is not a valid empty data buffer, and passes through any
errors that are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_export_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_export_start} initiates a
@code{gpgme_op_export} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{keydata} is not a valid empty data buffer.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_export_ext (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}[]}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_export} extracts public keys and returns
them in the data buffer @var{keydata}.  The output format of the key
data returned is determined by the @acronym{ASCII} armor attribute set
for the context @var{ctx}, or, if that is not set, by the encoding
specified for @var{keydata}.

If @var{pattern} or @var{*pattern} is @code{NULL}, all available keys
are returned.  Otherwise, @var{pattern} is a @code{NULL} terminated
array of strings that are used to limit the list to all keys matching
at least one of the patterns verbatim.

@var{mode} is usually 0; other values are described above.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{keydata} is not a valid empty data buffer, and passes through any
errors that are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_export_ext_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{pattern}[]}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_export_ext_start} initiates a
@code{gpgme_op_export_ext} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{keydata} is not a valid empty data buffer.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_export_keys (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t keys[]}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
@since{1.2.0}

The function @code{gpgme_op_export_keys} extracts public keys and returns
them in the data buffer @var{keydata}.  The output format of the key
data returned is determined by the @acronym{ASCII} armor attribute set
for the context @var{ctx}, or, if that is not set, by the encoding
specified for @var{keydata}.

The keys to export are taken form the @code{NULL} terminated array
@var{keys}.  Only keys of the currently selected protocol of
@var{ctx} which do have a fingerprint set are considered for export.
Other keys specified by the @var{keys} are ignored.  In particular
OpenPGP keys retrieved via an external key listing are not included.

@var{mode} is usually 0; other values are described above.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{keydata} is not a valid empty data buffer, @code{GPG_ERR_NO_DATA}
if no useful keys are in @var{keys} and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_export_keys_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{keys}[]}, @w{gpgme_export_mode_t @var{mode}}, @w{gpgme_data_t @var{keydata}})
@since{1.2.0}

The function @code{gpgme_op_export_keys_start} initiates a
@code{gpgme_op_export_ext} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{keydata} is not a valid empty data buffer, @code{GPG_ERR_NO_DATA}
if no useful keys are in @var{keys} and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun


@node Importing Keys
@subsection Importing Keys
@cindex key, import
@cindex key ring, import to

Importing keys means the same as running @command{gpg} with the command
@option{--import}.


@deftypefun gpgme_error_t gpgme_op_import (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_import} adds the keys in the data buffer
@var{keydata} to the key ring of the crypto engine used by @var{ctx}.
The format of @var{keydata} can be @acronym{ASCII} armored, for example,
but the details are specific to the crypto engine.

After the operation completed successfully, the result can be
retrieved with @code{gpgme_op_import_result}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import was completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{keydata} is not a valid pointer,
and @code{GPG_ERR_NO_DATA} if @var{keydata} is an empty data buffer.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_import_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{keydata}})
The function @code{gpgme_op_import_start} initiates a
@code{gpgme_op_import} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{keydata} is not a valid pointer, and
@code{GPG_ERR_NO_DATA} if @var{keydata} is an empty data buffer.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_import_keys (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t *@var{keys}})
@since{1.2.0}

The function @code{gpgme_op_import_keys} adds the keys described by
the @code{NULL} terminated array @var{keys} to the key ring of the
crypto engine used by @var{ctx}.  It is used to actually import and
make keys permanent which have been retrieved from an external source
(i.e., using @code{GPGME_KEYLIST_MODE_EXTERN}) earlier.  The external
keylisting must have been made with the same context configuration (in
particular the same home directory).  @footnote{Thus it is a
replacement for the usual workaround of exporting and then importing a
key to make an X.509 key permanent.}  Note that for OpenPGP this may
require another access to the keyserver over the network.

Only keys of the currently selected protocol of @var{ctx} are
considered for import.  Other keys specified by the @var{keys} are
ignored.  As of now all considered keys must have been retrieved using
the same method, i.e., the used key listing mode must be identical.

After the operation completed successfully, the result can be
retrieved with @code{gpgme_op_import_result}.

To move keys from one home directory to another, export and import the
keydata using @code{gpgme_op_export} and @code{gpgme_op_import}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import was completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, @code{GPG_ERR_CONFLICT} if the key
listing mode does not match, and @code{GPG_ERR_NO_DATA} if no keys were
considered for import.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_import_keys_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t *@var{keys}})
@since{1.2.0}

The function @code{gpgme_op_import_keys_start} initiates a
@code{gpgme_op_import_keys} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import was started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, @code{GPG_ERR_CONFLICT} if the key
listing mode does not match, and @code{GPG_ERR_NO_DATA} if no keys were
considered for import.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_receive_keys (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{keyids}[]})
@since{1.17.0}

The function @code{gpgme_op_receive_keys} adds the keys described by
the @code{NULL} terminated array @var{keyids} to the key ring of the
crypto engine used by @var{ctx}.  It is used to retrieve and import keys
from an external source.  This function currently works only for OpenPGP.

After the operation completed successfully, the result can be
retrieved with @code{gpgme_op_import_result}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import was completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and @code{GPG_ERR_NO_DATA} if no keys
were considered for import.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_receive_keys_start (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{keyids}[]})
@since{1.17.0}

The function @code{gpgme_op_receive_keys_start} initiates a
@code{gpgme_op_receive_keys} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
import was started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} is not a valid pointer, and @code{GPG_ERR_NO_DATA} if no keys
were considered for import.
@end deftypefun

@deftp {Data type} {gpgme_import_status_t}
This is a pointer to a structure used to store a part of the result of
a @code{gpgme_op_import} operation.  For each considered key one
status is added that contains information about the result of the
import.  The structure contains the following members:

@table @code
@item gpgme_import_status_t next
This is a pointer to the next status structure in the linked list, or
@code{NULL} if this is the last element.

@item char *fpr
This is the fingerprint of the key that was considered, or @code{NULL}
if the fingerprint of the key is not known, e.g., because the key to
import was encrypted and decryption failed.

@item gpgme_error_t result
If the import was not successful, this is the error value that caused
the import to fail.  Otherwise the error code is
@code{GPG_ERR_NO_ERROR}.

@item unsigned int status
This is a bit-wise OR of the following flags that give more
information about what part of the key was imported.  If the key was
already known, this might be 0.

@table @code
@item GPGME_IMPORT_NEW
The key was new.

@item GPGME_IMPORT_UID
The key contained new user IDs.

@item GPGME_IMPORT_SIG
The key contained new signatures.

@item GPGME_IMPORT_SUBKEY
The key contained new sub keys.

@item GPGME_IMPORT_SECRET
The key contained a secret key.
@end table
@end table
@end deftp

@deftp {Data type} {gpgme_import_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_import} operation.  After a successful import
operation, you can retrieve the pointer to the result with
@code{gpgme_op_import_result}.  The structure contains the following
members:

@table @code
@item int considered
The total number of considered keys.

@item int no_user_id
The number of keys without user ID.

@item int imported
The total number of imported keys.

@item int imported_rsa
The number of imported RSA keys.

@item int unchanged
The number of unchanged keys.

@item int new_user_ids
The number of new user IDs.

@item int new_sub_keys
The number of new sub keys.

@item int new_signatures
The number of new signatures.

@item int new_revocations
The number of new revocations.

@item int secret_read
The total number of secret keys read.

@item int secret_imported
The number of imported secret keys.

@item int secret_unchanged
The number of unchanged secret keys.

@item int not_imported
The number of keys not imported.

@item gpgme_import_status_t imports
A list of gpgme_import_status_t objects which contain more information
about the keys for which an import was attempted.

@item int skipped_v3_keys
For security reasons modern versions of GnuPG do not anymore support
v3 keys (created with PGP 2.x) and ignores them on import.  This
counter provides the number of such skipped v3 keys.

@end table
@end deftp

@deftypefun gpgme_import_result_t gpgme_op_import_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_import_result} returns a
@code{gpgme_import_result_t} pointer to a structure holding the result
of a @code{gpgme_op_import} operation.  The pointer is only valid if
the last operation on the context was a @code{gpgme_op_import} or
@code{gpgme_op_import_start} operation, and if this operation finished
successfully.  The returned pointer is only valid until the next
operation is started on the context.
@end deftypefun

@node Deleting Keys
@subsection Deleting Keys
@cindex key, delete
@cindex key ring, delete from

@deftypefun gpgme_error_t gpgme_op_delete_ext (@w{gpgme_ctx_t @var{ctx}}, @w{const gpgme_key_t @var{key}}, @w{unsigned int @var{flags}})
@since{1.9.1}

The function @code{gpgme_op_delete_ext} deletes the key @var{key} from
the key ring of the crypto engine used by @var{ctx}.

@var{flags} can be set to the bit-wise OR of the following flags:

@table @code
@item GPGME_DELETE_ALLOW_SECRET
@since{1.9.1}

If not set, only public keys are deleted. If set, secret keys are
deleted as well, if that is supported.

@item GPGME_DELETE_FORCE
@since{1.9.1}

If set, the user is not asked to confirm the deletion.
@end table

The function returns the error code @code{GPG_ERR_NO_ERROR} if the key
was deleted successfully, @code{GPG_ERR_INV_VALUE} if @var{ctx} or
@var{key} is not a valid pointer, @code{GPG_ERR_NO_PUBKEY} if
@var{key} could not be found in the keyring,
@code{GPG_ERR_AMBIGUOUS_NAME} if the key was not specified
unambiguously, and @code{GPG_ERR_CONFLICT} if the secret key for
@var{key} is available, but @var{allow_secret} is zero.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_delete_ext_start (@w{gpgme_ctx_t @var{ctx}}, @w{const gpgme_key_t @var{key}}, @w{unsigned int @var{flags}})
@since{1.9.1}

The function @code{gpgme_op_delete_ext_start} initiates a
@code{gpgme_op_delete} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation was started successfully, and @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{key} is not a valid pointer.
@end deftypefun

The following functions allow only to use one particular flag.  Their
use is thus deprecated.

@deftypefun gpgme_error_t gpgme_op_delete (@w{gpgme_ctx_t @var{ctx}}, @w{const gpgme_key_t @var{key}}, @w{int @var{allow_secret}})
Similar to @code{gpgme_op_delete_ext}, but only the flag
@code{GPGME_DELETE_ALLOW_SECRET} can be provided.  Actually all true
values are mapped to this flag.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_delete_start (@w{gpgme_ctx_t @var{ctx}}, @w{const gpgme_key_t @var{key}}, @w{int @var{allow_secret}})
Similar to @code{gpgme_op_delete_ext_start}, but only the flag
@code{GPGME_DELETE_ALLOW_SECRET} can be provided.  Actually all true
values are mapped to this flag.
@end deftypefun


@node Changing Passphrases
@subsection Changing Passphrases
@cindex passphrase, change

@deftypefun gpgme_error_t gpgme_op_passwd      @
             (@w{gpgme_ctx_t @var{ctx}},       @
              @w{const gpgme_key_t @var{key}}, @
              @w{unsigned int @var{flags}})

@since{1.3.0}

The function @code{gpgme_op_passwd} changes the passphrase of the
private key associated with @var{key}.  The only allowed value for
@var{flags} is @code{0}.  The backend engine will usually popup a window
to ask for the old and the new passphrase.  Thus this function is not
useful in a server application (where passphrases are not required
anyway).

Note that old @code{gpg} engines (before version 2.0.15) do not support
this command and will silently ignore it.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_passwd_start      @
             (@w{gpgme_ctx_t @var{ctx}},       @
              @w{const gpgme_key_t @var{key}}, @
              @w{unsigned int @var{flags}})

@since{1.3.0}

The function @code{gpgme_op_passwd_start} initiates a
@code{gpgme_op_passwd} operation.    It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns @code{0} if the operation was started successfully,
and an error code if one of the arguments is not valid or the oepration
could not be started.
@end deftypefun


@c
@c  CHANGING TOFU DATA
@c
@node Changing TOFU Data
@subsection Changing TOFU Data
@cindex validity, TOFU

The OpenPGP engine features a Trust-On-First-Use (TOFU) key validation
model.  For resolving conflicts it is necessary to declare the policy
for a key.  See the GnuPG manual for details on the TOFU
implementation.

@deftp {Data type} {enum gpgme_tofu_policy_t}
@since{1.7.0}

@tindex gpgme_tofu_policy_t
The @code{gpgme_tofu_policy_t} type specifies the set of possible
policy values that are supported by @acronym{GPGME}:

@table @code
@item GPGME_TOFU_POLICY_AUTO
Set the policy to ``auto''.
@item GPGME_TOFU_POLICY_GOOD
Set the policy to ``good''.
@item GPGME_TOFU_POLICY_BAD
Set the policy to ``bad''.
@item GPGME_TOFU_POLICY_ASK
Set the policy to ``ask''.
@item GPGME_TOFU_POLICY_UNKNOWN
Set the policy to ``unknown''.
@end table

@end deftp

To change the policy for a key the following functions can be used:

@deftypefun gpgme_error_t gpgme_op_tofu_policy @
             (@w{gpgme_ctx_t @var{ctx}},       @
              @w{const gpgme_key_t @var{key}}, @
              @w{gpgme_tofu_policy_t @var{policy}})

@since{1.7.0}

The function @code{gpgme_op_tofu_policy} changes the TOFU policy of
@var{key}.  The valid values for @var{policy} are listed above.  As of
now this function does only work for OpenPGP and requires at least
version 2.1.10 of GnuPG.

The function returns zero on success, @code{GPG_ERR_NOT_SUPPORTED} if
the engine does not support the command, or a bunch of other error
codes.

@end deftypefun

@deftypefun gpgme_error_t gpgme_op_tofu_policy_start @
             (@w{gpgme_ctx_t @var{ctx}},       @
              @w{const gpgme_key_t @var{key}}, @
              @w{gpgme_tofu_policy_t @var{policy}})

@since{1.7.0}

The function @code{gpgme_op_tofu_policy_start} initiates a
@code{gpgme_op_tofu_policy} operation.    It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns @code{0} if the operation was started successfully,
and an error code if one of the arguments is not valid or the oepration
could not be started.

@end deftypefun


@node Advanced Key Editing
@subsection Advanced Key Editing
@cindex key, edit

@deftp {Data type} {gpgme_error_t (*gpgme_interact_cb_t) @
   (@w{void *@var{handle}}, @
    @w{const char *@var{status}}, @
    @w{const char *@var{args}}, @
    @w{int @var{fd}})}

@since{1.7.0}

@tindex gpgme_interact_cb_t
The @code{gpgme_interact_cb_t} type is the type of functions which
@acronym{GPGME} calls if it a key interact operation is on-going.  The
status keyword @var{status} and the argument line @var{args} are
passed through by @acronym{GPGME} from the crypto engine.  An empty
string represents EOF.  The file descriptor @var{fd} is -1 for normal
status messages.  If @var{status} indicates a command rather than a
status message, the response to the command should be written to
@var{fd}.  The @var{handle} is provided by the user at start of
operation.

The function should return @code{GPG_ERR_FALSE} if it did not handle
the status code, @code{0} for success, or any other error value.
@end deftp

@deftypefun gpgme_error_t gpgme_op_interact (@w{gpgme_ctx_t @var{ctx}}, @
   @w{gpgme_key_t @var{key}}, @
   @w{unsigned int @var{flags}}, @
   @w{gpgme_interact_cb_t @var{fnc}}, @
   @w{void *@var{handle}}, @
   @w{gpgme_data_t @var{out}})

@since{1.7.0}

The function @code{gpgme_op_interact} processes the key @var{KEY}
interactively, using the interact callback function @var{FNC} with the
handle @var{HANDLE}.  The callback is invoked for every status and
command request from the crypto engine.  The output of the crypto
engine is written to the data object @var{out}.

Note that the protocol between the callback function and the crypto
engine is specific to the crypto engine and no further support in
implementing this protocol correctly is provided by @acronym{GPGME}.

@var{flags} modifies the behaviour of the function; the only defined
bit value is:

@table @code
@item GPGME_INTERACT_CARD
@since{1.7.0}

This is used for smartcard based keys and uses gpg’s
@code{--card-edit} command.

@end table

The function returns @code{0} if the edit operation completes
successfully, @code{GPG_ERR_INV_VALUE} if @var{ctx} or @var{key} is
not a valid pointer, and any error returned by the crypto engine or
the edit callback handler.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_interact_start (@w{gpgme_ctx_t @var{ctx}}, @
   @w{gpgme_key_t @var{key}}, @
   @w{unsigned int @var{flags}}, @
   @w{gpgme_interact_cb_t @var{fnc}}, @
   @w{void *@var{handle}}, @
   @w{gpgme_data_t @var{out}})

@since{1.7.0}

The function @code{gpgme_op_interact_start} initiates a
@code{gpgme_op_interact} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns @code{0} if the operation was started
successfully, and @code{GPG_ERR_INV_VALUE} if @var{ctx} or @var{key}
is not a valid pointer.
@end deftypefun


@node Crypto Operations
@section Crypto Operations
@cindex cryptographic operation

Sometimes, the result of a crypto operation returns a list of invalid
keys encountered in processing the request.  The following structure
is used to hold information about such a key.

@deftp {Data type} {gpgme_invalid_key_t}
This is a pointer to a structure used to store a part of the result of
a crypto operation which takes user IDs as one input parameter.  The
structure contains the following members:

@table @code
@item gpgme_invalid_key_t next
This is a pointer to the next invalid key structure in the linked
list, or @code{NULL} if this is the last element.

@item char *fpr
The fingerprint or key ID of the invalid key encountered.

@item gpgme_error_t reason
An error code describing the reason why the key was found invalid.
@end table
@end deftp


@menu
* Decrypt::                       Decrypting a ciphertext.
* Verify::                        Verifying a signature.
* Decrypt and Verify::            Decrypting a signed ciphertext.
* Sign::                          Creating a signature.
* Encrypt::                       Encrypting a plaintext.
* Random::                        Getting strong random bytes.
@end menu


@node Decrypt
@subsection Decrypt
@cindex decryption
@cindex cryptographic operation, decryption

@deftypefun gpgme_error_t gpgme_op_decrypt (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{cipher}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_decrypt} decrypts the ciphertext in the
data object @var{cipher} or, if a file name is set on the data object,
the ciphertext stored in the corresponding file.  The decrypted
ciphertext is stored into the data object @var{plain} or written to
the file set with @code{gpgme_data_set_file_name} for the data object
@var{plain}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
ciphertext could be decrypted successfully, @code{GPG_ERR_INV_VALUE}
if @var{ctx}, @var{cipher} or @var{plain} is not a valid pointer,
@code{GPG_ERR_NO_DATA} if @var{cipher} does not contain any data to
decrypt, @code{GPG_ERR_DECRYPT_FAILED} if @var{cipher} is not a valid
cipher text, @code{GPG_ERR_BAD_PASSPHRASE} if the passphrase for the
secret key could not be retrieved, and passes through some errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_decrypt_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{cipher}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_decrypt_start} initiates a
@code{gpgme_op_decrypt} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{cipher} or @var{plain} is not a valid pointer.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_decrypt_ext ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{gpgme_decrypt_flags_t @var{flags}}, @
            @w{gpgme_data_t @var{cipher}}, @
            @w{gpgme_data_t @var{plain}})

@since{1.8.0}

The function @code{gpgme_op_decrypt_ext} is the same as
@code{gpgme_op_decrypt} but has an additional argument
@var{flags}.  If @var{flags} is 0 both function behave identically.

If the flag @code{GPGME_DECRYPT_ARCHIVE} is set, then an encrypted
archive in the data object @var{cipher} is decrypted and extracted.
The content of the archive is extracted into a directory named
@code{GPGARCH_n_} (where @code{n} is a number) or into the directory
set with @code{gpgme_data_set_file_name} for the data object @var{plain}.

The value in @var{flags} is a bitwise-or combination of one or
multiple of the following bit values:

@table @code
@item GPGME_DECRYPT_VERIFY
@since{1.8.0}

The @code{GPGME_DECRYPT_VERIFY} symbol specifies that this function
shall exactly act as @code{gpgme_op_decrypt_verify}.

@item GPGME_DECRYPT_ARCHIVE
@since{1.19.0}

The @code{GPGME_DECRYPT_ARCHIVE} symbol specifies that the input is an
encrypted archive that shall be decrypted and extracted.  This feature
is currently only supported for the OpenPGP crypto engine and requires
GnuPG 2.4.1.

@item GPGME_DECRYPT_UNWRAP
@since{1.8.0}

The @code{GPGME_DECRYPT_UNWRAP} symbol specifies that the output shall
be an OpenPGP message with only the encryption layer removed.  This
requires GnuPG 2.1.12 and works only for OpenPGP.  This is the
counterpart to @code{GPGME_ENCRYPT_WRAP}.

@item GPGME_DECRYPT_LIST
@since{2.0.0}

The @code{GPGME_DECRYPT_LIST} symbol specifies that the actual
decryption step of an OpenPGP message shall be skipped.  This can be
used to information on the keyids of the recipients of some encrypted
data.  Note that most other result items have no or no useful
information in this case.

@end table

The function returns the error codes as described for
@code{gpgme_op_decrypt}.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_decrypt_ext_start ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{gpgme_decrypt_flags_t @var{flags}}, @
            @w{gpgme_data_t @var{cipher}}, @
            @w{gpgme_data_t @var{plain}})

@since{1.8.0}

The function @code{gpgme_op_decrypt_ext_start} initiates a
@code{gpgme_op_decrypt_ext} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{cipher} or @var{plain} is not a valid pointer.
@end deftypefun


@deftp {Data type} {gpgme_recipient_t}
@since{1.1.0}

This is a pointer to a structure used to store information about the
recipient of an encrypted text which is decrypted in a
@code{gpgme_op_decrypt} operation.  This information (except for the
status field) is even available before the operation finished
successfully, for example in a passphrase callback.  The structure
contains the following members:

@table @code
@item gpgme_recipient_t next
This is a pointer to the next recipient structure in the linked list,
or @code{NULL} if this is the last element.

@item gpgme_pubkey_algo_t
The public key algorithm used in the encryption.

@item char *keyid
This is the key ID of the key (in hexadecimal digits) used as
recipient.

@item gpgme_error_t status
This is an error number with the error code GPG_ERR_NO_SECKEY if the
secret key for this recipient is not available, and 0 otherwise.
@end table
@end deftp

@deftp {Data type} {gpgme_decrypt_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_decrypt} operation.  After successfully decrypting
data, you can retrieve the pointer to the result with
@code{gpgme_op_decrypt_result}.  As with all result structures, it
this structure shall be considered read-only and an application must
not allocate such a strucure on its own.  The structure contains the
following members:

@table @code
@item char *unsupported_algorithm
If an unsupported algorithm was encountered, this string describes the
algorithm that is not supported.

@item unsigned int wrong_key_usage : 1
@since{0.9.0}
This is true if the key was not used according to its policy.

@item unsigned int legacy_cipher_nomdc : 1
@since{1.11.2}
The message was made by a legacy algorithm without any integrity
protection.  This might be an old but legitimate message.

@item unsigned int is_mime : 1;
@since{1.11.0}
The message claims that the content is a MIME object.

@item unsigned int is_de_vs : 1;
@since{1.10.0}
The message was encrypted in a VS-NfD compliant way.  This is a
specification in Germany for a restricted communication level.

@item unsigned int beta_compliance : 1;
@since{1.24.0}
The compliance flags (e.g. is_de_vs) are set but the software has not
yet been approved or is in a beta state.

@item gpgme_recipient_t recipients
@since{1.1.0}

This is a linked list of recipients to which this message was encrypted.

@item char *file_name
This is the filename of the original plaintext message file if it is
known, otherwise this is a null pointer.

@item char *session_key
@since{1.8.0}

A textual representation (nul-terminated string) of the session key
used in symmetric encryption of the message, if the context has been
set to export session keys (see @code{gpgme_set_ctx_flag,
"export-session-key"}), and a session key was available for the most
recent decryption operation.  Otherwise, this is a null pointer.

You must not try to access this member of the struct unless
@code{gpgme_set_ctx_flag (ctx, "export-session-key")} returns success
or @code{gpgme_get_ctx_flag (ctx, "export-session-key")} returns true
(non-empty string).

@item char *symkey_algo
@since{1.11.0}

A string with the symmetric encryption algorithm and mode using the
format "<algo>.<mode>".  Note that the deprecated non-MDC encryption mode of
OpenPGP is given as "PGPCFB".

@end table
@end deftp

@deftypefun gpgme_decrypt_result_t gpgme_op_decrypt_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_decrypt_result} returns a
@code{gpgme_decrypt_result_t} pointer to a structure holding the
result of a @code{gpgme_op_decrypt} operation.  The pointer is only
valid if the last operation on the context was a
@code{gpgme_op_decrypt} or @code{gpgme_op_decrypt_start} operation.
If the operation failed this might be a @code{NULL} pointer.  The
returned pointer is only valid until the next operation is started on
the context.
@end deftypefun


@node Verify
@subsection Verify
@cindex verification
@cindex signature, verification
@cindex cryptographic operation, verification
@cindex cryptographic operation, signature check
@cindex signature notation data
@cindex notation data

@deftypefun gpgme_error_t gpgme_op_verify (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{sig}}, @w{gpgme_data_t @var{signed_text}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_verify} verifies that the signature in the
data object @var{sig} is a valid signature.  If @var{sig} is a
detached signature, then the signed text should be provided in
@var{signed_text} and @var{plain} should be a null pointer.
Otherwise, if @var{sig} is a normal (or cleartext) signature,
@var{signed_text} should be a null pointer and @var{plain} should be a
writable data object that will contain the plaintext after successful
verification.  If a file name is set on the data object @var{sig} (or
on the data object @var{signed_text}), then the data of the signature
(resp. the data of the signed text) is not read from the data object
but from the file with the given file name.  If a file name is set on
the data object @var{plain} then the plaintext is not stored in the
data object but it is written to a file with the given file name.

The results of the individual signature verifications can be retrieved
with @code{gpgme_op_verify_result}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be completed successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{sig} or @var{plain} is not a valid pointer,
@code{GPG_ERR_NO_DATA} if @var{sig} does not contain any data to
verify, and passes through any errors that are reported by the crypto
engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_verify_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{sig}}, @w{gpgme_data_t @var{signed_text}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_verify_start} initiates a
@code{gpgme_op_verify} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{sig} or @var{plain} is not a valid pointer, and
@code{GPG_ERR_NO_DATA} if @var{sig} or @var{plain} does not contain
any data to verify.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_verify_ext ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{gpgme_verify_flags_t @var{flags}}, @
            @w{gpgme_data_t @var{sig}}, @
            @w{gpgme_data_t @var{signed_text}}, @
            @w{gpgme_data_t @var{plain}})

The function @code{gpgme_op_verify_ext} is the same as
@code{gpgme_op_verify} but has an additional argument
@var{flags}.  If @var{flags} is 0 both function behave identically.

If the flag @code{GPGME_VERIFY_ARCHIVE} is set, then a signed archive
in the data object @var{sig} is verified and extracted. The content of
the archive is extracted into a directory named @code{GPGARCH_n_}
(where @code{n} is a number) or into the directory set with
@code{gpgme_data_set_file_name} for the data object @var{plain}.

The value in @var{flags} is a bitwise-or combination of one or
multiple of the following bit values:

@table @code
@item GPGME_VERIFY_ARCHIVE
@since{1.19.0}

The @code{GPGME_VERIFY_ARCHIVE} symbol specifies that the input is a
signed archive that shall be verified and extracted.  This feature
is currently only supported for the OpenPGP crypto engine and requires
GnuPG 2.4.1.

@end table

The function returns the error codes as descriped for
@code{gpgme_op_decrypt} respective @code{gpgme_op_encrypt}.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_verify_ext_start ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{gpgme_verify_flags_t @var{flags}}, @
            @w{gpgme_data_t @var{sig}}, @
            @w{gpgme_data_t @var{signed_text}}, @
            @w{gpgme_data_t @var{plain}})

The function @code{gpgme_op_verify_ext_start} initiates a
@code{gpgme_op_verify_ext} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{sig} or @var{plain} is not a valid pointer, and
@code{GPG_ERR_NO_DATA} if @var{sig} or @var{plain} does not contain
any data to verify.
@end deftypefun


@deftp {Data type} {gpgme_sig_notation_t}
This is a pointer to a structure used to store a part of the result of
a @code{gpgme_op_verify} operation.  The structure contains the
following members:

@table @code
@item gpgme_sig_notation_t next
This is a pointer to the next new signature notation structure in the
linked list, or @code{NULL} if this is the last element.

@item char *name
The name of the notation field.  If this is @code{NULL}, then the
member @code{value} will contain a policy URL.

@item int name_len
The length of the @code{name} field.  For strings the length is
counted without the trailing binary zero.

@item char *value
The value of the notation field.  If @code{name} is @code{NULL}, then
this is a policy URL.

@item int value_len
The length of the @code{value} field.  For strings the length is
counted without the trailing binary zero.

@item gpgme_sig_notation_flags_t flags
The accumulated flags field.  This field contains the flags associated
with the notation data in an accumulated form which can be used as an
argument to the function @code{gpgme_sig_notation_add}.  The value
@code{flags} is a bitwise-or combination of one or multiple of the
following bit values:

@table @code
@item GPGME_SIG_NOTATION_HUMAN_READABLE
@since{1.1.0}

The @code{GPGME_SIG_NOTATION_HUMAN_READABLE} symbol specifies that the
notation data is in human readable form

@item GPGME_SIG_NOTATION_CRITICAL
@since{1.1.0}

The @code{GPGME_SIG_NOTATION_CRITICAL} symbol specifies that the
notation data is critical.

@end table

@item unsigned int human_readable : 1
This is true if the @code{GPGME_SIG_NOTATION_HUMAN_READABLE} flag is
set and false otherwise.  This flag is only valid for notation data,
not for policy URLs.

@item unsigned int critical : 1
This is true if the @code{GPGME_SIG_NOTATION_CRITICAL} flag is set and
false otherwise.  This flag is valid for notation data and policy URLs.

@end table
@end deftp

@deftp {Data type} {gpgme_signature_t}
This is a pointer to a structure used to store a part of the result of
a @code{gpgme_op_verify} operation.  The structure contains the
following members:

@table @code
@item gpgme_signature_t next
This is a pointer to the next new signature structure in the linked
list, or @code{NULL} if this is the last element.

@item gpgme_sigsum_t summary
This is a bit vector giving a summary of the signature status.  It
provides an easy interface to a defined semantic of the signature
status.  Checking just one bit is sufficient to see whether a
signature is valid without any restrictions. This means that
you can check for GPGME_SIGSUM_VALID like this:

@example
if ((sig.summary & GPGME_SIGSUM_VALID))
@{
   ..do stuff if valid..
@}
else
@{
   ..do stuff if not fully valid..
@}
@end example

The defined bits are:
  @table @code
  @item GPGME_SIGSUM_VALID
  The signature is fully valid.

  @item GPGME_SIGSUM_GREEN
  The signature is good but one might want to display some extra
  information.  Check the other bits.

  @item GPGME_SIGSUM_RED
  The signature is bad. It might be useful to check other bits and
  display more information, i.e., a revoked certificate might not render a
  signature invalid when the message was received prior to the cause for
  the revocation.

  @item GPGME_SIGSUM_KEY_REVOKED
  The key or at least one certificate has been revoked.

  @item GPGME_SIGSUM_KEY_EXPIRED
  The key or one of the certificates has expired. It is probably a good
  idea to display the date of the expiration.

  @item GPGME_SIGSUM_SIG_EXPIRED
  The signature has expired.

  @item GPGME_SIGSUM_KEY_MISSING
  Can't verify due to a missing key or certificate.

  @item GPGME_SIGSUM_CRL_MISSING
  The CRL (or an equivalent mechanism) is not available.

  @item GPGME_SIGSUM_CRL_TOO_OLD
  Available CRL is too old.

  @item GPGME_SIGSUM_BAD_POLICY
  A policy requirement was not met.

  @item GPGME_SIGSUM_SYS_ERROR
  A system error occurred.

  @item GPGME_SIGSUM_TOFU_CONFLICT
  A TOFU conflict was detected.
  @end table

@item char *fpr
This is the fingerprint or key ID of the signature.

@item gpgme_error_t status
This is the status of the signature.  In particular, the following
status codes are of interest:

  @table @code
  @item GPG_ERR_NO_ERROR
  This status indicates that the signature could be verified or that
  there is no signature.  For the combined result this status
  means that all signatures could be verified.

  Note: This does not mean that a valid signature could be found.  Check
  the @code{summary} field for that.

  For example a @code{gpgme_op_decrypt_verify} returns a verification
  result with GPG_ERR_NO_ERROR for encrypted but unsigned data.

  @item GPG_ERR_SIG_EXPIRED
  This status indicates that the signature is valid but expired.  For
  the combined result this status means that all signatures are valid
  and expired.

  @item GPG_ERR_KEY_EXPIRED
  This status indicates that the signature is valid but the key used to
  verify the signature has expired.  For the combined result this status
  means that all signatures are valid and all keys are expired.

  @item GPG_ERR_CERT_REVOKED
  This status indicates that the signature is valid but the key used
  to verify the signature has been revoked.  For the combined result
  this status means that all signatures are valid and all keys are
  revoked.

  @item GPG_ERR_BAD_SIGNATURE
  This status indicates that the signature is invalid.  For the combined
  result this status means that all signatures are invalid.

  @item GPG_ERR_NO_PUBKEY
  This status indicates that the signature could not be verified due to
  a missing key.  For the combined result this status means that all
  signatures could not be checked due to missing keys.

  @item GPG_ERR_GENERAL
  This status indicates that there was some other error which prevented
  the signature verification.
  @end table

@item gpgme_sig_notation_t notations
This is a linked list with the notation data and policy URLs.

@item unsigned long timestamp
The creation timestamp of this signature.

@item unsigned long exp_timestamp
The expiration timestamp of this signature, or 0 if the signature does
not expire.

@item unsigned int wrong_key_usage : 1
This is true if the key was not used according to its policy.

@item unsigned int pka_trust : 2
This is set to the trust information gained by means of the PKA system.
Values are:
  @table @code
  @item 0
        No PKA information available or verification not possible.
  @item 1
        PKA verification failed.
  @item 2
        PKA verification succeeded.
  @item 3
        Reserved for future use.
  @end table
Depending on the configuration of the engine, this metric may also be
reflected by the validity of the signature.

@item unsigned int chain_model : 1
@since{1.1.6}

This is true if the validity of the signature has been checked using the
chain model.  In the chain model the time the signature has been created
must be within the validity period of the certificate and the time the
certificate itself has been created must be within the validity period
of the issuing certificate.  In contrast the default validation model
checks the validity of signature as well at the entire certificate chain
at the current time.

@item unsigned int is_de_vs : 1;
@since{1.10.0}
The signature was created in a VS-NfD compliant way.  This is a
specification in Germany for a restricted communication level.

@item unsigned int beta_compliance : 1;
@since{1.24.0}
The compliance flags (e.g. is_de_vs) are set but the software has not
yet been approved or is in a beta state.

@item gpgme_validity_t validity
The validity of the signature.

@item gpgme_error_t validity_reason
If a signature is not valid, this provides a reason why.

@item gpgme_pubkey_algo_t
The public key algorithm used to create this signature.

@item gpgme_hash_algo_t
The hash algorithm used to create this signature.

@item char *pka_address
The mailbox from the PKA information or @code{NULL}.

@item gpgme_key_t key
@since{1.7.0}

An object describing the key used to create the signature.  This key
object may be incomplete in that it only conveys information
availabale directly with a signature.  It may also be @code{NULL} if
such information is not readily available.

@end table
@end deftp

@deftp {Data type} {gpgme_verify_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_verify} operation.  After verifying a signature, you
can retrieve the pointer to the result with
@code{gpgme_op_verify_result}.  If the operation failed this might be
a @code{NULL} pointer.  The structure contains the following member:

@table @code
@item gpgme_signature_t signatures
A linked list with information about all signatures for which a
verification was attempted.

@item char *file_name
This is the filename of the original plaintext message file if it is
known, otherwise this is a null pointer.  Warning: The filename is
not covered by the signature.

@item unsigned int is_mime : 1;
@since{1.11.0}

The message claims that the content is a MIME object.  Warning: This
flag is not covered by the signature.

@end table
@end deftp

@deftypefun gpgme_verify_result_t gpgme_op_verify_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_verify_result} returns a
@code{gpgme_verify_result_t} pointer to a structure holding the result
of a @code{gpgme_op_verify} operation.  The pointer is only valid if
the last operation on the context was a @code{gpgme_op_verify},
@code{gpgme_op_verify_start}, @code{gpgme_op_decrypt_verify} or
@code{gpgme_op_decrypt_verify_start} operation, and if this operation
finished successfully (for @code{gpgme_op_decrypt_verify} and
@code{gpgme_op_decrypt_verify_start}, the error code
@code{GPG_ERR_NO_DATA} counts as successful in this context).  The
returned pointer is only valid until the next operation is started on
the context.
@end deftypefun


@node Decrypt and Verify
@subsection Decrypt and Verify
@cindex decryption and verification
@cindex verification and decryption
@cindex signature check
@cindex cryptographic operation, decryption and verification

@deftypefun gpgme_error_t gpgme_op_decrypt_verify (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{cipher}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_decrypt_verify} decrypts the ciphertext in
the data object @var{cipher} and stores it into the data object
@var{plain}.  If @var{cipher} contains signatures, they will be
verified.

After the operation completed, @code{gpgme_op_decrypt_result} and
@code{gpgme_op_verify_result} can be used to retrieve more information
about the signatures.

If the error code @code{GPG_ERR_NO_DATA} is returned, @var{cipher}
does not contain any data to decrypt.  However, it might still be
signed.  The information about detected signatures is available with
@code{gpgme_op_verify_result} in this case.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
ciphertext could be decrypted successfully, @code{GPG_ERR_INV_VALUE}
if @var{ctx}, @var{cipher} or @var{plain} is not a valid pointer,
@code{GPG_ERR_NO_DATA} if @var{cipher} does not contain any data to
decrypt, @code{GPG_ERR_DECRYPT_FAILED} if @var{cipher} is not a valid
cipher text, @code{GPG_ERR_BAD_PASSPHRASE} if the passphrase for the
secret key could not be retrieved, and passes through any errors that
are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_decrypt_verify_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{cipher}}, @w{gpgme_data_t @var{plain}})
The function @code{gpgme_op_decrypt_verify_start} initiates a
@code{gpgme_op_decrypt_verify} operation.  It can be completed by
calling @code{gpgme_wait} on the context.  @xref{Waiting For
Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{cipher}, @var{plain} or @var{r_stat} is not a valid
pointer, and @code{GPG_ERR_NO_DATA} if @var{cipher} does not contain
any data to decrypt.
@end deftypefun

When processing mails it is sometimes useful to extract the actual
mail address (the ``addr-spec'') from a string.  GPGME provides this
helper function which uses the same semantics as the internal
functions in GPGME and GnuPG:

@deftypefun @w{char *} gpgme_addrspec_from_uid (@w{const char *@var{uid}})

@since{1.7.1}

Return the mail address (called ``addr-spec'' in RFC-5322) from the
string @var{uid} which is assumed to be a user id (called ``address''
in RFC-5322).  All plain ASCII characters (i.e., those with bit 7
cleared) in the result are converted to lowercase.  Caller must free
the result using @code{gpgme_free}.  Returns @code{NULL} if no valid
address was found (in which case @code{ERRNO} is set to @code{EINVAL})
or for other errors.

@end deftypefun


@node Sign
@subsection Sign
@cindex signature, creation
@cindex sign
@cindex cryptographic operation, signing

A signature can contain signatures by one or more keys.  The set of
keys used to create a signatures is contained in a context, and is
applied to all following signing operations in this context (until the
set is changed).

@menu
* Selecting Signers::             How to choose the keys to sign with.
* Creating a Signature::          How to create a signature.
* Signature Notation Data::       How to add notation data to a signature.
@end menu


@node Selecting Signers
@subsubsection Selecting Signers
@cindex signature, selecting signers
@cindex signers, selecting

The key or the keys used to create a signature are stored in the
context.  The following functions can be used to manipulate this list.
If no signer has been set into the context a default key is used for
signing.

@deftypefun void gpgme_signers_clear (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_signers_clear} releases a reference for each
key on the signers list and removes the list of signers from the
context @var{ctx}.

Every context starts with an empty list.
@end deftypefun

@deftypefun gpgme_error_t gpgme_signers_add (@w{gpgme_ctx_t @var{ctx}}, @w{const gpgme_key_t @var{key}})
The function @code{gpgme_signers_add} adds the key @var{key} to the
list of signers in the context @var{ctx}.  If the key has the
subkey_match flag set (i.e. it was found via a fingerprint with '!'
suffix) that specific subkey is used for signing.

Calling this function acquires an additional reference for the key.
@end deftypefun

@deftypefun @w{unsigned int} gpgme_signers_count (@w{const gpgme_ctx_t @var{ctx}})
@since{1.4.3}

The function @code{gpgme_signers_count} returns the number of signer keys in
the context @var{ctx}.
@end deftypefun

@deftypefun gpgme_key_t gpgme_signers_enum (@w{const gpgme_ctx_t @var{ctx}}, @w{int @var{seq}})
The function @code{gpgme_signers_enum} returns the @var{seq}th key in
the list of signers in the context @var{ctx}.  An additional reference
is acquired for the user.

If @var{seq} is out of range, @code{NULL} is returned.
@end deftypefun


@node Creating a Signature
@subsubsection Creating a Signature

@deftp {Data type} {enum gpgme_sig_mode_t}
@tindex gpgme_sig_mode_t
The @code{gpgme_sig_mode_t} type is used to specify the desired type of a
signature.  The following modes are available:

@table @code
@item GPGME_SIG_MODE_NORMAL
A normal signature is made, the output includes the plaintext and the
signature.

@item GPGME_SIG_MODE_DETACH
A detached signature is made.

@item GPGME_SIG_MODE_CLEAR
A clear text signature is made.  The @acronym{ASCII} armor and text
mode settings of the context are ignored.

@item GPGME_SIG_MODE_ARCHIVE
@since{1.19.0}

A signed archive is created from the given files and directories.  This
feature is currently only supported for the OpenPGP crypto engine and requires
GnuPG 2.4.1.

@item GPGME_SIG_MODE_FILE
@since{1.24.0}

The filename set with @code{gpgme_data_set_file_name} for the data object
@var{plain} is passed to gpg, so that gpg reads the plaintext directly from
this file instead of from the data object @var{plain}.  This flag can be
combined with @code{GPGME_SIG_MODE_NORMAL}, @code{GPGME_SIG_MODE_DETACH},
and @code{GPGME_SIG_MODE_CLEAR}, but not with @code{GPGME_SIG_MODE_ARCHIVE}.
This feature is currently only supported for the OpenPGP crypto engine.

@end table
@end deftp

@deftypefun gpgme_error_t gpgme_op_sign (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{sig}}, @w{gpgme_sig_mode_t @var{mode}})
The function @code{gpgme_op_sign} creates a signature for the text in
the data object @var{plain} and returns it in the data object
@var{sig} or writes it directly to the file set with
@code{gpgme_data_set_file_name} for the data object @var{sig}.  The type
of the signature created is determined by the @acronym{ASCII} armor (or,
if that is not set, by the encoding specified for @var{sig}), the text
mode attributes set for the context @var{ctx} and the requested signature
mode @var{mode}.

If the signature mode flag @code{GPGME_SIG_MODE_FILE} is set and a filename
has been set with @code{gpgme_data_set_file_name} for the data object
@var{plain}, then this filename is passed to gpg, so that gpg reads the
plaintext directly from this file instead of from the data object @var{plain}.

If signature mode @code{GPGME_SIG_MODE_ARCHIVE} is requested then a
signed archive is created from the files and directories given as
NUL-separated list in the data object @var{plain}.  The paths of the files
and directories have to be given as paths relative to the current working
directory or relative to the base directory set with
@code{gpgme_data_set_file_name} for the data object @var{plain}.

After the operation completed successfully, the result can be
retrieved with @code{gpgme_op_sign_result}.

If an S/MIME signed message is created using the CMS crypto engine,
the number of certificates to include in the message can be specified
with @code{gpgme_set_include_certs}.  @xref{Included Certificates}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
signature could be created successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{plain} or @var{sig} is not a valid pointer,
@code{GPG_ERR_NO_DATA} if the signature could not be created,
@code{GPG_ERR_BAD_PASSPHRASE} if the passphrase for the secret key
could not be retrieved, @code{GPG_ERR_UNUSABLE_SECKEY} if there are
invalid signers, and passes through any errors that are reported by the
crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_sign_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{sig}}, @w{gpgme_sig_mode_t @var{mode}})
The function @code{gpgme_op_sign_start} initiates a
@code{gpgme_op_sign} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the operation could be
started successfully, and @code{GPG_ERR_INV_VALUE} if @var{ctx},
@var{plain} or @var{sig} is not a valid pointer.
@end deftypefun

@deftp {Data type} {gpgme_new_signature_t}
This is a pointer to a structure used to store a part of the result of
a @code{gpgme_op_sign} operation.  The structure contains the
following members:

@table @code
@item gpgme_new_signature_t next
This is a pointer to the next new signature structure in the linked
list, or @code{NULL} if this is the last element.

@item gpgme_sig_mode_t type
The type of this signature.

@item gpgme_pubkey_algo_t pubkey_algo
The public key algorithm used to create this signature.

@item gpgme_hash_algo_t hash_algo
The hash algorithm used to create this signature.

@item unsigned int sig_class
The signature class of this signature.  Note that only the values 0,
1, and 2 are well-defined.

@item unsigned long int timestamp
The creation timestamp of this signature.

@item char *fpr
The fingerprint of the key which was used to create this signature.
@end table
@end deftp

@deftp {Data type} {gpgme_sign_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_sign} operation.  After successfully generating a
signature, you can retrieve the pointer to the result with
@code{gpgme_op_sign_result}.  The structure contains the following
members:

@table @code
@item gpgme_invalid_key_t invalid_signers
A linked list with information about all invalid keys for which a
signature could not be created.

@item gpgme_new_signature_t signatures
A linked list with information about all signatures created.
@end table
@end deftp

@deftypefun gpgme_sign_result_t gpgme_op_sign_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_sign_result} returns a
@code{gpgme_sign_result_t} pointer to a structure holding the result
of a @code{gpgme_op_sign} operation.  The pointer is only valid if the
last operation on the context was a @code{gpgme_op_sign},
@code{gpgme_op_sign_start}, @code{gpgme_op_encrypt_sign} or
@code{gpgme_op_encrypt_sign_start} operation.  If that operation
failed, the function might return a @code{NULL} pointer. The returned
pointer is only valid until the next operation is started on the
context.
@end deftypefun


@node Signature Notation Data
@subsubsection Signature Notation Data
@cindex notation data
@cindex signature notation data
@cindex policy URL

Using the following functions, you can attach arbitrary notation data
to a signature.  This information is then available to the user when
the signature is verified.

@deftypefun void gpgme_sig_notation_clear (@w{gpgme_ctx_t @var{ctx}})
@since{1.1.0}

The function @code{gpgme_sig_notation_clear} removes the notation data
from the context @var{ctx}.  Subsequent signing operations from this
context will not include any notation data.

Every context starts with an empty notation data list.
@end deftypefun

@deftypefun gpgme_error_t gpgme_sig_notation_add (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{name}}, @w{const char *@var{value}}, @w{gpgme_sig_notation_flags_t @var{flags}})
@since{1.1.0}

The function @code{gpgme_sig_notation_add} adds the notation data with
the name @var{name} and the value @var{value} to the context
@var{ctx}.

Subsequent signing operations will include this notation data, as well
as any other notation data that was added since the creation of the
context or the last @code{gpgme_sig_notation_clear} operation.

The arguments @var{name} and @var{value} must be @code{NUL}-terminated
strings in human-readable form.  The flag
@code{GPGME_SIG_NOTATION_HUMAN_READABLE} is implied
(non-human-readable notation data is currently not supported).  The
strings must be in UTF-8 encoding.

If @var{name} is @code{NULL}, then @var{value} should be a policy URL.

The function @code{gpgme_sig_notation_add} returns the error code
@code{GPG_ERR_NO_ERROR} if the notation data could be added
successfully, @code{GPG_ERR_INV_VALUE} if @var{ctx} is not a valid
pointer, or if @var{name}, @var{value} and @var{flags} are an invalid
combination.  The function also passes through any errors that are
reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_sig_notation_t gpgme_sig_notation_get (@w{const gpgme_ctx_t @var{ctx}})
@since{1.1.0}

The function @code{gpgme_sig_notation_get} returns the linked list of
notation data structures that are contained in the context @var{ctx}.

If @var{ctx} is not a valid pointer, or there is no notation data
added for this context, @code{NULL} is returned.
@end deftypefun


@node Encrypt
@subsection Encrypt
@cindex encryption
@cindex cryptographic operation, encryption

One plaintext can be encrypted for several recipients at the same
time.  The list of recipients is created independently of any context,
and then passed to the encryption operation.

@menu
* Encrypting a Plaintext::        How to encrypt a plaintext.
@end menu


@node Encrypting a Plaintext
@subsubsection Encrypting a Plaintext

@deftypefun gpgme_error_t gpgme_op_encrypt (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{recp}[]}, @w{gpgme_encrypt_flags_t @var{flags}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})
The function @code{gpgme_op_encrypt} encrypts the plaintext in the
data object @var{plain} for the recipients @var{recp} and stores the
ciphertext in the data object @var{cipher} or writes it directly to the
file set with @code{gpgme_data_set_file_name} for the data object
@var{cipher}.  The type of the ciphertext created is determined by the
@acronym{ASCII} armor (or, if that is not set, by the encoding specified
for @var{cipher}) and the text mode attributes set for the context
@var{ctx}.  If a filename has been set with @code{gpgme_data_set_file_name}
for the data object @var{plain} then this filename is stored in the
ciphertext.

If the flag @code{GPGME_ENCRYPT_FILE} is set and a filename has been set
with @code{gpgme_data_set_file_name} for the data object @var{plain},
then this filename is passed to gpg, so that gpg reads the plaintext
directly from this file instead of from the data object @var{plain}.

If the flag @code{GPGME_ENCRYPT_ARCHIVE} is set, then an encrypted
archive is created from the files and directories given as NUL-separated
list in the data object @var{plain}.  The paths of the files and
directories have to be given as paths relative to the current working
directory or relative to the base directory set with
@code{gpgme_data_set_file_name} for the data object @var{plain}.

@var{recp} must be a @code{NULL}-terminated array of keys.  The user
must keep references for all keys during the whole duration of the
call (but see @code{gpgme_op_encrypt_start} for the requirements with
the asynchronous variant).

The value in @var{flags} is a bitwise-or combination of one or
multiple of the following bit values:

@table @code
@item GPGME_ENCRYPT_ALWAYS_TRUST
The @code{GPGME_ENCRYPT_ALWAYS_TRUST} symbol specifies that all the
recipients in @var{recp} should be trusted, even if the keys do not
have a high enough validity in the keyring.  This flag should be used
with care; in general it is not a good idea to use any untrusted keys.

For the S/MIME (CMS) protocol this flag allows to encrypt to a
certificate without running any checks on the validity of the
certificate.

@item GPGME_ENCRYPT_NO_ENCRYPT_TO
@since{1.2.0}

The @code{GPGME_ENCRYPT_NO_ENCRYPT_TO} symbol specifies that no
default or hidden default recipients as configured in the crypto
backend should be included.  This can be useful for managing different
user profiles.

@item GPGME_ENCRYPT_NO_COMPRESS
@since{1.5.0}

The @code{GPGME_ENCRYPT_NO_COMPRESS} symbol specifies that the
plaintext shall not be compressed before it is encrypted.  This is
in some cases useful if the length of the encrypted message
may reveal information about the plaintext.

@item GPGME_ENCRYPT_PREPARE
@itemx GPGME_ENCRYPT_EXPECT_SIGN
The @code{GPGME_ENCRYPT_PREPARE} symbol is used with the UI Server
protocol to prepare an encryption (i.e., sending the
@code{PREP_ENCRYPT} command).  With the
@code{GPGME_ENCRYPT_EXPECT_SIGN} symbol the UI Server is advised to
also expect a sign command.

@item GPGME_ENCRYPT_SYMMETRIC
@since{1.7.0}

The @code{GPGME_ENCRYPT_SYMMETRIC} symbol specifies that the
output should be additionally encrypted symmetrically even
if recipients are provided. This feature is only supported
for the OpenPGP crypto engine.

@item GPGME_ENCRYPT_ADD_RECP
@item GPGME_ENCRYPT_CHG_RECP
@since{1.24.0}

Instead of encrypting, decrypt the input and write an output which is
additionally encrypted to the specified keys.  The CHG flag is similar
but does not add encryption to the specified keys but existing
encryption keys by the new ones.  This feature is only supported for
the OpenPGP crypto engine and requires at least GnuPG version 2.5.1.


@item GPGME_ENCRYPT_THROW_KEYIDS
@since{1.8.0}

The @code{GPGME_ENCRYPT_THROW_KEYIDS} symbols requests that the
identifiers for the decrption keys are not included in the ciphertext.
On the receiving side, the use of this flag may slow down the
decryption process because all available secret keys must be tried.
This flag is only honored for OpenPGP encryption.

@item GPGME_ENCRYPT_WRAP
@since{1.8.0}

The @code{GPGME_ENCRYPT_WRAP} symbol specifies that the input is an
OpenPGP message and not a plain data.  This is the counterpart to
@code{GPGME_DECRYPT_UNWRAP}.

@item GPGME_ENCRYPT_WANT_ADDRESS
@since{1.11.0}

The @code{GPGME_ENCRYPT_WANT_ADDRESS} symbol requests that all
supplied keys or key specifications include a syntactically valid mail
address.  If this is not the case the operation is not even tried and
the error code @code{GPG_ERR_INV_USER_ID} is returned.  Only the
address part of the key specification is conveyed to the backend.  As
of now the key must be specified using the @var{recpstring} argument
of the extended encrypt functions.  This feature is currently only
supported for the OpenPGP crypto engine.

@item GPGME_ENCRYPT_ARCHIVE
@since{1.19.0}

The @code{GPGME_ENCRYPT_ARCHIVE} symbol specifies that the input is a
NUL-separated list of file paths and directory paths that shall be
encrypted into an archive.  This feature is currently only supported
for the OpenPGP crypto engine and requires GnuPG 2.4.1.

@item GPGME_ENCRYPT_FILE
@since{1.24.0}

The @code{GPGME_ENCRYPT_FILE} symbol specifies that the filename set
with @code{gpgme_data_set_file_name} for the data object @var{plain}
is passed to gpg, so that gpg reads the plaintext directly from this
file instead of from the data object @var{plain}.  This feature is
currently only supported for the OpenPGP crypto engine.

@end table

If @code{GPG_ERR_UNUSABLE_PUBKEY} is returned, some recipients in
@var{recp} are invalid, but not all.  In this case the plaintext might
be encrypted for all valid recipients and returned in @var{cipher} (if
this happens depends on the crypto engine).  More information about
the invalid recipients is available with
@code{gpgme_op_encrypt_result}.

If @var{recp} is @code{NULL}, symmetric rather than public key
encryption is performed.  Symmetrically encrypted cipher text can be
deciphered with @code{gpgme_op_decrypt}.  Note that in this case the
crypto backend needs to retrieve a passphrase from the user.
Symmetric encryption is currently only supported for the OpenPGP
crypto backend.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
ciphertext could be created successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{recp}, @var{plain} or @var{cipher} is not a valid
pointer, @code{GPG_ERR_UNUSABLE_PUBKEY} if @var{recp} contains some
invalid recipients, @code{GPG_ERR_BAD_PASSPHRASE} if the passphrase
for the symmetric key could not be retrieved, and passes through any
errors that are reported by the crypto engine support routines.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_encrypt_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{recp}[]}, @w{gpgme_encrypt_flags_t @var{flags}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})
The function @code{gpgme_op_encrypt_start} initiates a
@code{gpgme_op_encrypt} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

References to the keys only need to be held for the duration of this
call.  The user can release its references to the keys after this
function returns, even if the operation is not yet finished.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx}, @var{rset}, @var{plain} or @var{cipher} is not a valid
pointer, and @code{GPG_ERR_UNUSABLE_PUBKEY} if @var{rset} does not
contain any valid recipients.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_encrypt_ext @
            (@w{gpgme_ctx_t @var{ctx}}, @
             @w{gpgme_key_t @var{recp}[]}, @
             @w{const char *@var{recpstring}}, @
             @w{gpgme_encrypt_flags_t @var{flags}}, @
             @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})

@since{1.11.0}

This is an extended version of @code{gpgme_op_encrypt} with
@var{recpstring} as additional parameter.  If @var{recp} is NULL and
@var{recpstring} is not NULL, the latter is expected to be a linefeed
delimited string with the set of key specifications.  In contrast to
@var{recp} the keys are given directly as strings and there is no need
to first create key objects.  Leading and trailing white space is
remove from each line in @var{recpstring}.  The keys are then passed
verbatim to the backend engine.

For the OpenPGP backend several special keywords are supported to
modify the operation.  These keywords are given instead of a key
specification.  The currently supported keywords are:

@table @code
@item --hidden
@itemx --no-hidden
These keywords toggle between normal and hidden recipients for all
following key specifications.  When a hidden recipient is requested
the gpg option @option{-R} (or @option{-F} in file mode) is used
instead of @option{-r} (@option{-f} in file mode).

@item --file
@itemx --no-file
These keywords toggle between regular and file mode for all following
key specification.  In file mode the option @option{-f} or @option{-F}
is passed to gpg.  At least GnuPG version 2.1.14 is required to handle
these options.  The @code{GPGME_ENCRYPT_WANT_ADDRESS} flag is ignored
in file mode.

@item --
This keyword disables all keyword detection up to the end of the
string.  All keywords are treated as verbatim arguments.

@end table

To create a @var{recpstring} it is often useful to employ a strconcat
style function.  For example this function creates a string to encrypt
to two keys:

@example
char *
xbuild_recpstring (const char *key1, const char *key2)
@{
  char *result = gpgrt_strconcat ("--\n", key1, "\n", key2, NULL);
  if (!result)
    @{ perror ("strconcat failed"); exit (2); @}
  return result;
@}
@end example

Note the use of the double dash here; unless you want to specify a
keyword, it is a good idea to avoid any possible trouble with key
specifications starting with a double dash.  The used strconcat
function is available in Libgpg-error 1.28 and later; Libgpg-error
(aka Gpgrt) is a dependency of GPGME.  The number of arguments to
@code{gpgrt_strconcat} is limited to 47 but that should always be
sufficient.  In case a larger and non-fixed number of keys are to be
supplied the following code can be used:

@example
char *
xbuild_long_recpstring (void)
@{
  gpgrt_stream_t memfp;
  const char *s;
  void *result;

  memfp = gpgrt_fopenmem (0, "w+b");
  if (!memfp)
    @{ perror ("fopenmem failed"); exit (2); @}
  gpgrt_fputs ("--", memfp);
  while ((s = get_next_keyspec ()))
    @{
      gpgrt_fputc ('\n', memfp);
      gpgrt_fputs (s, memfp);
    @}
  gpgrt_fputc (0, memfp);
  if (gpgrt_ferror (memfp))
    @{ perror ("writing to memstream failed"); exit (2); @}
  if (gpgrt_fclose_snatch (memfp, &result, NULL))
    @{ perror ("fclose_snatch failed"); exit (2); @}
  return result;
@}
@end example

In this example @code{get_next_keyspec} is expected to return the next
key to be added to the string.  Please take care: Encrypting to a
large number of recipients is often questionable due to security
reasons and also for the technicality that all keys are currently
passed on the command line to @command{gpg} which has as a platform
specific length limitation.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_encrypt_ext_start @
            (@w{gpgme_ctx_t @var{ctx}}, @
             @w{gpgme_key_t @var{recp}[]}, @
             @w{const char *@var{recpstring}}, @
             @w{gpgme_encrypt_flags_t @var{flags}}, @
             @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})

@since{1.11.0}

This is an extended version of @code{gpgme_op_encrypt_start} with
@var{recpstring} as additional parameter.  If @var{recp} is NULL and
@var{recpstring} is not NULL, the latter is expected to be a linefeed
delimited string with the set of key specifications.  In contrast to
@var{recp} the keys are given directly as strings and there is no need
to first create key objects.  The keys are passed verbatim to the
backend engine.

@end deftypefun

@deftp {Data type} {gpgme_encrypt_result_t}
This is a pointer to a structure used to store the result of a
@code{gpgme_op_encrypt} operation.  After successfully encrypting
data, you can retrieve the pointer to the result with
@code{gpgme_op_encrypt_result}.  The structure contains the following
members:

@table @code
@item gpgme_invalid_key_t invalid_recipients
A linked list with information about all invalid keys for which
the data could not be encrypted.
@end table
@end deftp

@deftypefun gpgme_encrypt_result_t gpgme_op_encrypt_result (@w{gpgme_ctx_t @var{ctx}})
The function @code{gpgme_op_encrypt_result} returns a
@code{gpgme_encrypt_result_t} pointer to a structure holding the
result of a @code{gpgme_op_encrypt} operation.  The pointer is only
valid if the last operation on the context was a
@code{gpgme_op_encrypt}, @code{gpgme_op_encrypt_start},
@code{gpgme_op_sign} or @code{gpgme_op_sign_start} operation.  If this
operation failed, this might be a @code{NULL} pointer.  The returned
pointer is only valid until the next operation is started on the
context.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_encrypt_sign (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{recp}[]}, @w{gpgme_encrypt_flags_t @var{flags}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})
The function @code{gpgme_op_encrypt_sign} does a combined encrypt and
sign operation.  It is used like @code{gpgme_op_encrypt}, but the
ciphertext also contains signatures for the signers listed in
@var{ctx}.

The combined encrypt and sign operation is currently only available
for the OpenPGP crypto engine.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_encrypt_sign_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{recp}[]}, @w{gpgme_encrypt_flags_t @var{flags}}, @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})
The function @code{gpgme_op_encrypt_sign_start} initiates a
@code{gpgme_op_encrypt_sign} operation.  It can be completed by
calling @code{gpgme_wait} on the context.  @xref{Waiting For
Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation could be started successfully, and @code{GPG_ERR_INV_VALUE}
if @var{ctx}, @var{rset}, @var{plain} or @var{cipher} is not a valid
pointer.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_encrypt_sign_ext @
            (@w{gpgme_ctx_t @var{ctx}}, @
             @w{gpgme_key_t @var{recp}[]}, @
             @w{const char *@var{recpstring}}, @
             @w{gpgme_encrypt_flags_t @var{flags}}, @
             @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})

@since{1.11.0}

This is an extended version of @code{gpgme_op_encrypt_sign} with
@var{recpstring} as additional parameter.  If @var{recp} is NULL and
@var{recpstring} is not NULL, the latter is expected to be a linefeed
delimited string with the set of key specifications.  In contrast to
@var{recp} the keys are given directly as strings and there is no need
to first create the key objects.  The keys are passed verbatim to the
backend engine.

@end deftypefun

@deftypefun gpgme_error_t gpgme_op_encrypt_sign_ext_start @
            (@w{gpgme_ctx_t @var{ctx}}, @
             @w{gpgme_key_t @var{recp}[]}, @
             @w{const char *@var{recpstring}}, @
             @w{gpgme_encrypt_flags_t @var{flags}}, @
             @w{gpgme_data_t @var{plain}}, @w{gpgme_data_t @var{cipher}})

@since{1.11.0}

This is an extended version of @code{gpgme_op_encrypt_sign_start} with
@var{recpstring} as additional parameter.  If @var{recp} is NULL and
@var{recpstring} is not NULL, the latter is expected to be a linefeed
delimited string with the set of key specifications.  In contrast to
@var{recp} the keys are given directly as strings and there is no need
to first create the key objects.  The keys are passed verbatim to the
backend engine.

@end deftypefun

@node Random
@subsection Random
@cindex random bytes
@cindex cryptographic operation, random

GPGME provides a simple interface to get cryptographic strong random
numbers from Libgcrypt via the GPG engine.

@menu
* Getting Random::        How to get random bytes.
@end menu


@node Getting Random
@subsubsection How to get random bytes

@deftp {Data type} {enum gpgme_random_mode_t}
@tindex gpgme_random_mode_t

This enum ist used to select special modes of the random generator.

@table @code
@item GPGME_RANDOM_MODE_NORMAL
This is the standard mode, you may also use 0 instead of this enum
value.

@item GPGME_RANDOM_MODE_ZBASE32
This mode is used to tell the random function to return a 30 character
string with random characters from the zBase32 set of characters.  The
returned string will be terminated by a Nul.
@end table
@end deftp


@deftypefun {gpgme_error_t} gpgme_op_random_bytes ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{gpgme_random_mode_t @var{mode}}, @
            @w{char *@var{buffer}}, @
            @w{size_t @var{bufsize}})

@since{2.0.0}

The function @code{gpgme_op_random_bytes} returns random bytes.
@var{buffer} must be provided by the caller with a size of
@var{BUFSIZE} and will on return be filled with random bytes retrieved
from gpg.  However, if @var{mode} is @code{GPGME_RANDOM_MODE_ZBASE32}
@var{bufsize} needs to be at least 31 and will be filled with a string
of 30 ASCII characters followed by a Nul; the remainder of the buffer
is not changed.  The caller must provide a context @var{ctx}
initialized for GPGME_PROTOCOL_OPENPGP.  This function has a limit of
1024 bytes to avoid accidental overuse of the random generator

@end deftypefun

@deftypefun {gpgme_error_t} gpgme_op_random_values ( @
            @w{gpgme_ctx_t @var{ctx}}, @
            @w{size_t @var{limit}}, @
            @w{size_t *@var{retval}})

@since{2.0.0}

The function @code{gpgme_op_random_value} returns an unbiased random
value in the range 0 <= value < @var{limit}.  The value is returned at
@var{retval} if and only if the function returns with success.  The
caller must also provide a context @var{ctx} initialized for
GPGME_PROTOCOL_OPENPGP.
@end deftypefun



@node Miscellaneous
@section Miscellaneous operations

Here are some support functions which are sometimes useful.

@menu
* Running other Programs::      Running other Programs
* Using the Assuan protocol::   Using the Assuan protocol
* Checking for updates::        How to check for software updates
@end menu


@node Running other Programs
@subsection Running other Programs

GPGME features an internal subsystem to run the actual backend
engines.  Along with data abstraction object this subsystem can be
used to run arbitrary simple programs which even need not be related
to cryptographic features.  It may for example be used to run tools
which are part of the GnuPG system but are not directly accessible
with the GPGME API.


@deftypefun gpgme_error_t gpgme_op_spawn @
            (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{file}}, @
             @w{const char *@var{argv}[]}, @w{gpgme_data_t @var{datain}}, @
             @w{gpgme_data_t @var{dataout}}, @w{gpgme_data_t @var{dataerr}}, @
             @w{unsigned int @var{flags}})

@since{1.5.0}

The function @code{gpgme_op_spawn} runs the program @var{file} with
the arguments taken from the NULL terminated array @var{argv}.  If no
arguments are required @var{argv} may be given as @code{NULL}.  In the
latter case or if @code{argv[0]} is the empty string, GPGME uses the
basename of @var{file} for @code{argv[0]}.  The file descriptors
@code{stdin}, @code{stdout}, and @code{stderr} are connected to the
data objects @var{datain}, @var{dataout}, and @var{dataerr}.  If NULL
is passed for one of these data objects the corresponding file
descriptor is connected to @file{/dev/null}.

The value in @var{flags} is a bitwise-or combination of one or
multiple of the following bit values:

@table @code
@item GPGME_SPAWN_DETACHED
@since{1.5.0}

Under Windows this flag inhibits the allocation of a new console for
the program.  This is useful for a GUI application which needs to call
a command line helper tool.

@item GPGME_SPAWN_ALLOW_SET_FG
@since{1.5.0}

Under Windows this flag allows the called program to put itself into
the foreground.
@end table
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_spawn_start @
            (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{file}}, @
             @w{const char *@var{argv}[]}, @w{gpgme_data_t @var{datain}}, @
             @w{gpgme_data_t @var{dataout}}, @w{gpgme_data_t @var{dataerr}}, @
             @w{unsigned int @var{flags}})

@since{1.5.0}

This is the asynchronous variant of @code{gpgme_op_spawn}.
@end deftypefun


@node Using the Assuan protocol
@subsection Using the Assuan protocol

The Assuan protocol can be used to talk to arbitrary Assuan servers.
By default it is connected to the GnuPG agent, but it may be connected
to arbitrary servers by using @code{gpgme_ctx_set_engine_info},
passing the location of the servers socket as @var{file_name}
argument, and an empty string as @var{home_dir} argument.

The Assuan protocol functions use three kinds of callbacks to transfer
data:

@deftp {Data type} {gpgme_error_t (*gpgme_assuan_data_cb_t) @
       (@w{void *@var{opaque}}, @w{const void *@var{data}}, @
        @w{size_t @var{datalen}})}

@since{1.2.0}

This callback receives any data sent by the server.  @var{opaque} is
the pointer passed to @code{gpgme_op_assuan_transact_start},
@var{data} of length @var{datalen} refers to the data sent.
@end deftp

@deftp {Data type} {gpgme_error_t (*gpgme_assuan_inquire_cb_t) @
       (@w{void *@var{opaque}}, @w{const char *@var{name}}, @
        @w{const char *@var{args}}, @w{gpgme_data_t *@var{r_data}})}

@since{1.2.0}

This callback is used to provide additional data to the Assuan server.
@var{opaque} is the pointer passed to
@code{gpgme_op_assuan_transact_start}, @var{name} and @var{args}
specify what kind of data the server requested, and @var{r_data} is
used to return the actual data.

Note: Returning data is currently not implemented in @acronym{GPGME}.
@end deftp

@deftp {Data type} {gpgme_error_t (*gpgme_assuan_status_cb_t) @
       (@w{void *@var{opaque}}, @w{const char *@var{status}}, @
        @w{const char *@var{args}})}

@since{1.2.0}

This callback receives any status lines sent by the server.
@var{opaque} is the pointer passed to
@code{gpgme_op_assuan_transact_start}, @var{status} and @var{args}
denote the status update sent.
@end deftp

@deftypefun gpgme_error_t gpgme_op_assuan_transact_start @
            (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{command}}, @
             @w{gpgme_assuan_data_cb_t @var{data_cb}}, @
             @w{void * @var{data_cb_value}}, @
             @w{gpgme_assuan_inquire_cb_t @var{inquire_cb}}, @
             @w{void * @var{inquire_cb_value}}, @
             @w{gpgme_assuan_status_cb_t @var{status_cb}}, @
             @w{void * @var{status_cb_value}})

@since{1.2.0}

Send the Assuan @var{command} and return results via the callbacks.
Any callback may be @code{NULL}.  The result of the operation may be
retrieved using @code{gpgme_wait_ext}.

Asynchronous variant.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_assuan_transact_ext @
            (@w{gpgme_ctx_t @var{ctx}}, @w{const char *@var{command}}, @
             @w{gpgme_assuan_data_cb_t @var{data_cb}}, @
             @w{void * @var{data_cb_value}}, @
             @w{gpgme_assuan_inquire_cb_t @var{inquire_cb}}, @
             @w{void * @var{inquire_cb_value}}, @
             @w{gpgme_assuan_status_cb_t @var{status_cb}}, @
             @w{void * @var{status_cb_value}}, @
             @w{gpgme_error_t *@var{op_err}})

Send the Assuan @var{command} and return results via the callbacks.
The result of the operation is returned in @var{op_err}.

Synchronous variant.
@end deftypefun


@node Checking for updates
@subsection How to check for software updates

The GnuPG Project operates a server to query the current versions of
software packages related to GnuPG.  GPGME can be used to
access this online database and check whether a new version of a
software package is available.

@deftp {Data type} {gpgme_query_swdb_result_t}
@since{1.8.0}

This is a pointer to a structure used to store the result of a
@code{gpgme_op_query_swdb} operation.  After success full call to that
function, you can retrieve the pointer to the result with
@code{gpgme_op_query_swdb_result}.  The structure contains the
following member:

@table @code
@item name
This is the name of the package.

@item iversion
The currently installed version or an empty string.  This value is
either a copy of the argument given to @code{gpgme_op_query_swdb} or
the version of the installed software as figured out by GPGME or GnuPG.

@item created
This gives the date the file with the list of version numbers has
originally be created by the GnuPG project.

@item retrieved
This gives the date the file was downloaded.

@item warning
If this flag is set either an error has occurred or some of the
information in this structure are not properly set.  For example if
the version number of the installed software could not be figured out,
the @code{update} flag may not reflect a required update status.

@item update
If this flag is set an update of the software is available.

@item urgent
If this flag is set an available update is important.

@item noinfo
If this flag is set, no valid information could be retrieved.

@item unknown
If this flag is set the given @code{name} is not known.

@item tooold
If this flag is set the available information is not fresh enough.

@item error
If this flag is set some other error has occurred.

@item version
The version string of the latest released version.

@item reldate
The release date of the latest released version.

@end table
@end deftp

@deftypefun gpgme_error_t gpgme_op_query_swdb @
            (@w{gpgme_ctx_t @var{ctx}},       @
             @w{const char *@var{name}},      @
             @w{const char *@var{iversion}},  @
             @w{gpgme_data_t @var{reserved}})

@since{1.8.0}

Query the software version database for software package @var{name}
and check against the installed version given by @var{iversion}.  If
@var{iversion} is given as @code{NULL} a check is only done if GPGME
can figure out the version by itself (for example when using
"gpgme" or "gnupg").  If @code{NULL} is used for @var{name} the
current gpgme version is checked.  @var{reserved} must be set to 0.

@end deftypefun

@deftypefun gpgme_query_swdb_result_t gpgme_op_query_swdb_result @
            (@w{gpgme_ctx_t @var{ctx}})

@since{1.8.0}

The function @code{gpgme_op_query_swdb_result} returns a
@code{gpgme_query_swdb_result_t} pointer to a structure holding the
result of a @code{gpgme_op_query_swdb} operation.  The pointer is only
valid if the last operation on the context was a successful call to
@code{gpgme_op_query_swdb}.  If that call failed, the result might
be a @code{NULL} pointer.  The returned pointer is only valid until
the next operation is started on the context @var{ctx}.
@end deftypefun

@noindent
Here is an example on how to check whether GnuPG is current:

@example
#include <gpgme.h>

int
main (void)
@{
  gpg_error_t err;
  gpgme_ctx_t ctx;
  gpgme_query_swdb_result_t result;

  gpgme_check_version (NULL);
  err = gpgme_new (&ctx);
  if (err)
    fprintf (stderr, "error creating context: %s\n", gpg_strerror (err));
  else
    @{
      gpgme_set_protocol (ctx, GPGME_PROTOCOL_GPGCONF);

      err = gpgme_op_query_swdb (ctx, "gnupg", NULL, 0);
      if (err)
        fprintf (stderr, "error querying swdb: %s\n", gpg_strerror (err));
      else
        @{
          result = gpgme_op_query_swdb_result (ctx);
          if (!result)
            fprintf (stderr, "error querying swdb\n");
          if (!result->warning && !result->update)
            printf ("GnuPG version %s is current\n",
                    result->iversion);
          else if (!result->warning && result->update)
            printf ("GnuPG version %s can be updated to %s\n",
                    result->iversion, result->version);
          else
            fprintf (stderr, "error finding the update status\n");
        @}
      gpgme_release (ctx);
    @}
  return 0;
@}
@end example


@node Run Control
@section Run Control
@cindex run control
@cindex cryptographic operation, running

@acronym{GPGME} supports running operations synchronously and
asynchronously.  You can use asynchronous operation to set up a
context up to initiating the desired operation, but delay performing
it to a later point.

Furthermore, you can use an external event loop to control exactly
when @acronym{GPGME} runs.  This ensures that @acronym{GPGME} only
runs when necessary and also prevents it from blocking for a long
time.

@menu
* Waiting For Completion::        Waiting until an operation is completed.
* Using External Event Loops::    Advanced control over what happens when.
* Cancellation::                  How to end pending operations prematurely.
@end menu


@node Waiting For Completion
@subsection Waiting For Completion
@cindex cryptographic operation, wait for
@cindex wait for completion

@deftypefun gpgme_ctx_t gpgme_wait (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_error_t *@var{status}}, @w{int @var{hang}})
The function @code{gpgme_wait} continues the pending operation within
the context @var{ctx}.  In particular, it ensures the data exchange
between @acronym{GPGME} and the crypto backend and watches over the
run time status of the backend process.

If @var{hang} is true, the function does not return until the
operation is completed or cancelled.  Otherwise the function will not
block for a long time.

The error status of the finished operation is returned in @var{status}
if @code{gpgme_wait} does not return @code{NULL}.

The @var{ctx} argument can be @code{NULL}.  In that case,
@code{gpgme_wait} waits for any context to complete its operation.

@code{gpgme_wait} can be used only in conjunction with any context
that has a pending operation initiated with one of the
@code{gpgme_op_*_start} functions except @code{gpgme_op_keylist_start}
and @code{gpgme_op_trustlist_start} (for which you should use the
corresponding @code{gpgme_op_*_next} functions).  If @var{ctx} is
@code{NULL}, all of such contexts are waited upon and possibly
returned.  Synchronous operations running in parallel, as well as key
and trust item list operations, do not affect @code{gpgme_wait}.

In a multi-threaded environment, only one thread should ever call
@code{gpgme_wait} at any time, regardless of whether @var{ctx} is specified
or not.  This means that all calls to this function should be fully
synchronized by locking primitives.  It is safe to start asynchronous
operations while a thread is running in @code{gpgme_wait}.

The function returns the @var{ctx} of the context which has finished
the operation.  If @var{hang} is false, and the timeout expires,
@code{NULL} is returned and @code{*status} will be set to 0.  If an
error occurs, @code{NULL} is returned and the error is returned in
@code{*status}.
@end deftypefun


@node Using External Event Loops
@subsection Using External Event Loops
@cindex event loop, external

@acronym{GPGME} hides the complexity of the communication between the
library and the crypto engine.  The price of this convenience is that
the calling thread can block arbitrary long waiting for the data
returned by the crypto engine.  In single-threaded programs, in
particular if they are interactive, this is an unwanted side-effect.
OTOH, if @code{gpgme_wait} is used without the @var{hang} option being
enabled, it might be called unnecessarily often, wasting CPU time that
could be used otherwise.

The I/O callback interface described in this section lets the user
take control over what happens when.  @acronym{GPGME} will provide the
user with the file descriptors that should be monitored, and the
callback functions that should be invoked when a file descriptor is
ready for reading or writing.  It is then the user's responsibility to
decide when to check the file descriptors and when to invoke the
callback functions.  Usually this is done in an event loop, that also
checks for events in other parts of the program.  If the callback
functions are only called when the file descriptors are ready,
@acronym{GPGME} will never block.  This gives the user more control
over the program flow, and allows to perform other tasks when
@acronym{GPGME} would block otherwise.

By using this advanced mechanism, @acronym{GPGME} can be integrated
smoothly into GUI toolkits like GTK+ even for single-threaded
programs.

@menu
* I/O Callback Interface::        How I/O callbacks are registered.
* Registering I/O Callbacks::     How to use I/O callbacks for a context.
* I/O Callback Example::          An example how to use I/O callbacks.
* I/O Callback Example GTK+::     How to use @acronym{GPGME} with GTK+.
* I/O Callback Example GDK::      How to use @acronym{GPGME} with GDK.
* I/O Callback Example Qt::       How to use @acronym{GPGME} with Qt.
@end menu


@node I/O Callback Interface
@subsubsection I/O Callback Interface

@deftp {Data type} {gpgme_error_t (*gpgme_io_cb_t) (@w{void *@var{data}}, @w{int @var{fd}})}
@tindex gpgme_io_cb_t
The @code{gpgme_io_cb_t} type is the type of functions which
@acronym{GPGME} wants to register as I/O callback handlers using the
@code{gpgme_register_io_cb_t} functions provided by the user.

@var{data} and @var{fd} are provided by @acronym{GPGME} when the I/O
callback handler is registered, and should be passed through to the
handler when it is invoked by the user because it noticed activity on
the file descriptor @var{fd}.

The callback handler always returns @code{0}, but you should consider
the return value to be reserved for later use.
@end deftp

@deftp {Data type} {gpgme_error_t (*gpgme_register_io_cb_t) (@w{void *@var{data}}, @w{int @var{fd}}, @w{int @var{dir}}, @w{gpgme_io_cb_t @var{fnc}}, @w{void *@var{fnc_data}}, @w{void **@var{tag}})}
@tindex gpgme_register_io_cb_t
The @code{gpgme_register_io_cb_t} type is the type of functions which can
be called by @acronym{GPGME} to register an I/O callback function
@var{fnc} for the file descriptor @var{fd} with the user.
@var{fnc_data} should be passed as the first argument to @var{fnc}
when the handler is invoked (the second argument should be @var{fd}).
If @var{dir} is 0, @var{fnc} should be called by the user when
@var{fd} is ready for writing.  If @var{dir} is 1, @var{fnc} should be
called when @var{fd} is ready for reading.

@var{data} was provided by the user when registering the
@code{gpgme_register_io_cb_t} function with @acronym{GPGME} and will always
be passed as the first argument when registering a callback function.
For example, the user can use this to determine the event loop to
which the file descriptor should be added.

@acronym{GPGME} will call this function when a crypto operation is
initiated in a context for which the user has registered I/O callback
handler functions with @code{gpgme_set_io_cbs}.  It can also call this
function when it is in an I/O callback handler for a file descriptor
associated to this context.

The user should return a unique handle in @var{tag} identifying this
I/O callback registration, which will be passed to the
@code{gpgme_register_io_cb_t} function without interpretation when the file
descriptor should not be monitored anymore.
@end deftp

@deftp {Data type} {void (*gpgme_remove_io_cb_t) (@w{void *@var{tag}})}
The @code{gpgme_remove_io_cb_t} type is the type of functions which can be
called by @acronym{GPGME} to remove an I/O callback handler that was
registered before.  @var{tag} is the handle that was returned by the
@code{gpgme_register_io_cb_t} for this I/O callback.

@acronym{GPGME} can call this function when a crypto operation is in
an I/O callback.  It will also call this function when the context is
destroyed while an operation is pending.
@end deftp

@deftp {Data type} {enum gpgme_event_io_t}
@tindex gpgme_event_io_t
The @code{gpgme_event_io_t} type specifies the type of an event that is
reported to the user by @acronym{GPGME} as a consequence of an I/O
operation.  The following events are defined:

@table @code
@item GPGME_EVENT_START
The operation is fully initialized now, and you can start to run the
registered I/O callback handlers now.  Note that registered I/O
callback handlers must not be run before this event is signalled.
@var{type_data} is @code{NULL} and reserved for later use.

@item GPGME_EVENT_DONE
The operation is finished, the last I/O callback for this operation
was removed.  The accompanying @var{type_data} points to a
@code{struct gpgme_io_event_done_data} variable that contains the
status of the operation that finished.  This event is signalled after
the last I/O callback has been removed.

@item GPGME_EVENT_NEXT_KEY
In a @code{gpgme_op_keylist_start} operation, the next key was
received from the crypto engine.  The accompanying @var{type_data} is
a @code{gpgme_key_t} variable that contains the key with one reference
for the user.

@end table
@end deftp

@deftp {Data type} {void (*gpgme_event_io_cb_t) (@w{void *@var{data}}, @w{gpgme_event_io_t @var{type}}, @w{void *@var{type_data}})}
The @code{gpgme_event_io_cb_t} type is the type of functions which can be
called by @acronym{GPGME} to signal an event for an operation running
in a context which has I/O callback functions registered by the user.

@var{data} was provided by the user when registering the
@code{gpgme_event_io_cb_t} function with @acronym{GPGME} and will always be
passed as the first argument when registering a callback function.
For example, the user can use this to determine the context in which
this event has occurred.

@var{type} will specify the type of event that has occurred.
@var{type_data} specifies the event further, as described in the above
list of possible @code{gpgme_event_io_t} types.

@acronym{GPGME} can call this function in an I/O callback handler.
@end deftp


@node Registering I/O Callbacks
@subsubsection Registering I/O Callbacks

@deftp {Data type} {struct gpgme_io_cbs}
@tindex gpgme_event_io_t
This structure is used to store the I/O callback interface functions
described in the previous section.  It has the following members:

@table @code
@item gpgme_register_io_cb_t add
This is the function called by @acronym{GPGME} to register an I/O
callback handler.  It must be specified.

@item void *add_priv
This is passed as the first argument to the @code{add} function when
it is called by @acronym{GPGME}.  For example, it can be used to
determine the event loop to which the file descriptor should be added.

@item gpgme_remove_io_cb_t remove
This is the function called by @acronym{GPGME} to remove an I/O
callback handler.  It must be specified.

@item gpgme_event_io_cb_t event
This is the function called by @acronym{GPGME} to signal an event for
an operation.  It must be specified, because at least the start event
must be processed.

@item void *event_priv
This is passed as the first argument to the @code{event} function when
it is called by @acronym{GPGME}.  For example, it can be used to
determine the context in which the event has occurred.
@end table
@end deftp

@deftypefun void gpgme_set_io_cbs (@w{gpgme_ctx_t @var{ctx}}, @w{struct gpgme_io_cbs *@var{io_cbs}})
The function @code{gpgme_set_io_cbs} enables the I/O callback
interface for the context @var{ctx}.  The I/O callback functions are
specified by @var{io_cbs}.

If @var{io_cbs}->@code{add} is @code{NULL}, the I/O callback interface
is disabled for the context, and normal operation is restored.
@end deftypefun

@deftypefun void gpgme_get_io_cbs (@w{gpgme_ctx_t @var{ctx}}, @w{struct gpgme_io_cbs *@var{io_cbs}})
The function @code{gpgme_get_io_cbs} returns the I/O callback
functions set with @code{gpgme_set_io_cbs} in @var{io_cbs}.
@end deftypefun


@node I/O Callback Example
@subsubsection I/O Callback Example

To actually use an external event loop, you have to implement the I/O
callback functions that are used by @acronym{GPGME} to register and
unregister file descriptors.  Furthermore, you have to actually
monitor these file descriptors for activity and call the appropriate
I/O callbacks.

The following example illustrates how to do that.  The example uses
locking to show in which way the callbacks and the event loop can
run concurrently.  For the event loop, we use a fixed array.  For a
real-world implementation, you should use a dynamically sized
structure because the number of file descriptors needed for a crypto
operation in @acronym{GPGME} is not predictable.

@example
#include <assert.h>
#include <errno.h>
#include <stdlib.h>
#include <pthread.h>
#include <sys/types.h>
#include <gpgme.h>

/* The following structure holds the result of a crypto operation.  */
struct op_result
@{
  int done;
  gpgme_error_t err;
@};

/* The following structure holds the data associated with one I/O
callback.  */
struct one_fd
@{
  int fd;
  int dir;
  gpgme_io_cb_t fnc;
  void *fnc_data;
  void *loop;
@};

struct event_loop
@{
  pthread_mutex_t lock;
#define MAX_FDS 32
  /* Unused slots are marked with FD being -1.  */
  struct one_fd fds[MAX_FDS];
@};
@end example

The following functions implement the I/O callback interface.

@example
gpgme_error_t
add_io_cb (void *data, int fd, int dir, gpgme_io_cb_t fnc, void *fnc_data,
	   void **r_tag)
@{
  struct event_loop *loop = data;
  struct one_fd *fds = loop->fds;
  int i;

  pthread_mutex_lock (&loop->lock);
  for (i = 0; i < MAX_FDS; i++)
    @{
      if (fds[i].fd == -1)
	@{
	  fds[i].fd = fd;
	  fds[i].dir = dir;
	  fds[i].fnc = fnc;
	  fds[i].fnc_data = fnc_data;
	  fds[i].loop = loop;
	  break;
	@}
    @}
  pthread_mutex_unlock (&loop->lock);
  if (i == MAX_FDS)
    return gpg_error (GPG_ERR_GENERAL);
  *r_tag = &fds[i];
  return 0;
@}

void
remove_io_cb (void *tag)
@{
  struct one_fd *fd = tag;
  struct event_loop *loop = fd->loop;

  pthread_mutex_lock (&loop->lock);
  fd->fd = -1;
  pthread_mutex_unlock (&loop->lock);
@}

void
event_io_cb (void *data, gpgme_event_io_t type, void *type_data)
@{
  struct op_result *result = data;

  /* We don't support list operations here.  */
  if (type == GPGME_EVENT_DONE)
    @{
      result->done = 1;
      result->err = *type_data;
    @}
@}
@end example

The final missing piece is the event loop, which will be presented
next.  We only support waiting for the success of a single operation.

@example
int
do_select (struct event_loop *loop)
@{
  fd_set rfds;
  fd_set wfds;
  int i, n;
  int any = 0;
  struct timeval tv;
  struct one_fd *fdlist = loop->fds;

  pthread_mutex_lock (&loop->lock);
  FD_ZERO (&rfds);
  FD_ZERO (&wfds);
  for (i = 0; i < MAX_FDS; i++)
    if (fdlist[i].fd != -1)
      FD_SET (fdlist[i].fd, fdlist[i].dir ? &rfds : &wfds);
  pthread_mutex_unlock (&loop->lock);

  tv.tv_sec = 0;
  tv.tv_usec = 1000;

  do
    @{
      n = select (FD_SETSIZE, &rfds, &wfds, NULL, &tv);
    @}
  while (n < 0 && errno == EINTR);

  if (n < 0)
    return n;	/* Error or timeout.  */

  pthread_mutex_lock (&loop->lock);
  for (i = 0; i < MAX_FDS && n; i++)
    @{
      if (fdlist[i].fd != -1)
	@{
	  if (FD_ISSET (fdlist[i].fd, fdlist[i].dir ? &rfds : &wfds))
	    @{
	      assert (n);
	      n--;
	      any = 1;
              /* The I/O callback handler can register/remove callbacks,
                 so we have to unlock the file descriptor list.  */
              pthread_mutex_unlock (&loop->lock);
	      (*fdlist[i].fnc) (fdlist[i].fnc_data, fdlist[i].fd);
              pthread_mutex_lock (&loop->lock);
	    @}
	@}
    @}
  pthread_mutex_unlock (&loop->lock);
  return any;
@}

void
wait_for_op (struct event_loop *loop, struct op_result *result)
@{
  int ret;

  do
    @{
      ret = do_select (loop);
    @}
  while (ret >= 0 && !result->done);
@}
@end example

The main function shows how to put it all together.

@example
int
main (int argc, char *argv[])
@{
  struct event_loop loop;
  struct op_result result;
  gpgme_ctx_t ctx;
  gpgme_error_t err;
  gpgme_data_t sig, text;
  int i;
  pthread_mutexattr_t attr;
  struct gpgme_io_cbs io_cbs =
  @{
    add_io_cb,
    &loop,
    remove_io_cb,
    event_io_cb,
    &result
  @};

  init_gpgme ();

  /* Initialize the loop structure.  */

  /* The mutex must be recursive, since remove_io_cb (which acquires a
     lock) can be called while holding a lock acquired in do_select.  */
  pthread_mutexattr_init (&attr);
  pthread_mutexattr_settype (&attr, PTHREAD_MUTEX_RECURSIVE);
  pthread_mutex_init (&loop.lock, &attr);
  pthread_mutexattr_destroy (&attr);

  for (i = 0; i < MAX_FDS; i++)
    loop.fds[i].fd = -1;

  /* Initialize the result structure.  */
  result.done = 0;

  err = gpgme_data_new_from_file (&sig, "signature", 1);
  if (!err)
    err = gpgme_data_new_from_file (&text, "text", 1);
  if (!err)
    err = gpgme_new (&ctx);
  if (!err)
    @{
       gpgme_set_io_cbs (ctx, &io_cbs);
       err = gpgme_op_verify_start (ctx, sig, text, NULL);
    @}
  if (err)
    @{
      fprintf (stderr, "gpgme error: %s: %s\n",
               gpgme_strsource (err), gpgme_strerror (err));
      exit (1);
    @}

  wait_for_op (&loop, &result);
  if (!result.done)
    @{
      fprintf (stderr, "select error\n");
      exit (1);
    @}
  if (!result.err)
    @{
      fprintf (stderr, "verification failed: %s: %s\n",
               gpgme_strsource (result.err), gpgme_strerror (result.err));
      exit (1);
    @}
  /* Evaluate verify result.  */
  @dots{}
  return 0;
@}
@end example


@node I/O Callback Example GTK+
@subsubsection I/O Callback Example GTK+
@cindex GTK+, using @acronym{GPGME} with

The I/O callback interface can be used to integrate @acronym{GPGME}
with the GTK+ event loop.  The following code snippets shows how this
can be done using the appropriate register and remove I/O callback
functions.  In this example, the private data of the register I/O
callback function is unused.  The event notifications is missing
because it does not require any GTK+ specific setup.

@example
#include <gtk/gtk.h>

struct my_gpgme_io_cb
@{
  gpgme_io_cb_t fnc;
  void *fnc_data;
  guint input_handler_id
@};

void
my_gpgme_io_cb (gpointer data, gint source, GdkInputCondition condition)
@{
  struct my_gpgme_io_cb *iocb = data;
  (*(iocb->fnc)) (iocb->data, source);
@}

void
my_gpgme_remove_io_cb (void *data)
@{
  struct my_gpgme_io_cb *iocb = data;
  gtk_input_remove (data->input_handler_id);
@}

void
my_gpgme_register_io_callback (void *data, int fd, int dir, gpgme_io_cb_t fnc,
                               void *fnc_data, void **tag)
@{
  struct my_gpgme_io_cb *iocb = g_malloc (sizeof (struct my_gpgme_io_cb));
  iocb->fnc = fnc;
  iocb->data = fnc_data;
  iocb->input_handler_id = gtk_input_add_full (fd, dir
                                                   ? GDK_INPUT_READ
                                                   : GDK_INPUT_WRITE,
                                               my_gpgme_io_callback,
                                               0, iocb, NULL);
  *tag = iocb;
  return 0;
@}
@end example


@node I/O Callback Example GDK
@subsubsection I/O Callback Example GDK
@cindex GDK, using @acronym{GPGME} with

The I/O callback interface can also be used to integrate
@acronym{GPGME} with the GDK event loop.  The following code snippets
shows how this can be done using the appropriate register and remove
I/O callback functions.  In this example, the private data of the
register I/O callback function is unused.  The event notifications is
missing because it does not require any GDK specific setup.

It is very similar to the GTK+ example in the previous section.

@example
#include <gdk/gdk.h>

struct my_gpgme_io_cb
@{
  gpgme_io_cb_t fnc;
  void *fnc_data;
  gint tag;
@};

void
my_gpgme_io_cb (gpointer data, gint source, GdkInputCondition condition)
@{
  struct my_gpgme_io_cb *iocb = data;
  (*(iocb->fnc)) (iocb->data, source);
@}

void
my_gpgme_remove_io_cb (void *data)
@{
  struct my_gpgme_io_cb *iocb = data;
  gdk_input_remove (data->tag);
@}

void
my_gpgme_register_io_callback (void *data, int fd, int dir, gpgme_io_cb_t fnc,
                               void *fnc_data, void **tag)
@{
  struct my_gpgme_io_cb *iocb = g_malloc (sizeof (struct my_gpgme_io_cb));
  iocb->fnc = fnc;
  iocb->data = fnc_data;
  iocb->tag = gtk_input_add_full (fd, dir ? GDK_INPUT_READ : GDK_INPUT_WRITE,
                                  my_gpgme_io_callback, iocb, NULL);
  *tag = iocb;
  return 0;
@}
@end example


@node I/O Callback Example Qt
@subsubsection I/O Callback Example Qt
@cindex Qt, using @acronym{GPGME} with

The I/O callback interface can also be used to integrate
@acronym{GPGME} with the Qt event loop.  The following code snippets
show how this can be done using the appropriate register and remove
I/O callback functions.  In this example, the private data of the
register I/O callback function is unused.  The event notifications is
missing because it does not require any Qt specific setup.

@example
#include <qsocketnotifier.h>
#include <qapplication.h>

struct IOCB @{
  IOCB( GpgmeIOCb f, void * d, QSocketNotifier * n )
    : func( f ), data( d ), notifier( n ) @{@}
  GpgmeIOCb func;
  void * data;
  QSocketNotifier * notifier;
@}

class MyApp : public QApplication @{

  // ...

  static void registerGpgmeIOCallback( void * data, int fd, int dir,
                                       GpgmeIOCb func, void * func_data,
                                       void ** tag ) @{
    QSocketNotifier * n =
      new QSocketNotifier( fd, dir ? QSocketNotifier::Read
                                   : QSocketNotifier::Write );
    connect( n, SIGNAL(activated(int)),
             qApp, SLOT(slotGpgmeIOCallback(int)) );
    qApp->mIOCBs.push_back( IOCB( func, func_data, n ) );
    *tag = (void*)n;
  @}

  static void removeGpgmeIOCallback( void * tag ) @{
    if ( !tag ) return;
    QSocketNotifier * n = static_cast<QSocketNotifier*>( tag );
    for ( QValueList<IOCB>::iterator it = qApp->mIOCBs.begin() ;
          it != qApp->mIOCBs.end() ; ++it )
      if ( it->notifier == n ) @{
        delete it->notifier;
        qApp->mIOCBs.erase( it );
        return;
      @}
  @}

public slots:
  void slotGpgmeIOCallback( int fd ) @{
    for ( QValueList<IOCB>::const_iterator it = mIOCBs.begin() ;
          it != mIOCBs.end() ; ++it )
      if ( it->notifier && it->notifier->socket() == fd )
        (*(it->func)) ( it->func_data, fd );
  @}

  // ...

private:
  QValueList<IOCB> mIOCBs;
  // ...
@};
@end example


@node Cancellation
@subsection Cancellation
@cindex cryptographic operation, aborting
@cindex cryptographic operation, cancelling
@cindex aborting operations
@cindex cancelling operations

Sometimes you do not want to wait for an operation to finish.
@acronym{GPGME} provides two different functions to achieve that.  The
function @code{gpgme_cancel} takes effect immediately.  When it
returns, the operation is effectively canceled.  However, it has some
limitations and can not be used with synchronous operations.  In
contrast, the function @code{gpgme_cancel_async} can be used with any
context and from any thread, but it is not guaranteed to take effect
immediately.  Instead, cancellation occurs at the next possible time
(typically the next time I/O occurs in the target context).

@deftypefun gpgme_ctx_t gpgme_cancel (@w{gpgme_ctx_t @var{ctx}})
@since{0.4.5}

The function @code{gpgme_cancel} attempts to cancel a pending
operation in the context @var{ctx}.  This only works if you use the
global event loop or your own event loop.

If you use the global event loop, you must not call @code{gpgme_wait}
during cancellation.  After successful
cancellation, you can call @code{gpgme_wait} (optionally waiting on
@var{ctx}), and the context @var{ctx} will appear as if it had
finished with the error code @code{GPG_ERR_CANCEL}.

If you use an external event loop, you must ensure that no I/O
callbacks are invoked for this context (for example by halting the
event loop).  On successful cancellation, all registered I/O callbacks
for this context will be unregistered, and a @code{GPGME_EVENT_DONE}
event with the error code @code{GPG_ERR_CANCEL} will be signalled.

The function returns an error code if the cancellation failed (in this
case the state of @var{ctx} is not modified).
@end deftypefun


@deftypefun gpgme_ctx_t gpgme_cancel_async (@w{gpgme_ctx_t @var{ctx}})
@since{1.1.7}

The function @code{gpgme_cancel_async} attempts to cancel a pending
operation in the context @var{ctx}.  This can be called by any thread
at any time after starting an operation on the context, but will not
take effect immediately.  The actual cancellation happens at the next
time GPGME processes I/O in that context.

The function returns an error code if the cancellation failed (in this
case the state of @var{ctx} is not modified).
@end deftypefun

@c **********************************************************
@c *******************  Appendices  *************************
@c **********************************************************

@include uiserver.texi

@node Debugging
@appendix How to solve problems
@cindex debug
@cindex GPGME_DEBUG

Everyone knows that software often does not do what it should do and thus
there is a need to track down problems.  This is in particular true
for applications using a complex library like @acronym{GPGME} and of
course also for the library itself.  Here we give a few hints on how
to solve such problems.

First of all you should make sure that the keys you want to use are
installed in the GnuPG engine and are usable.  Thus the first test is
to run the desired operation using @command{gpg} or @command{gpgsm} on
the command line.  If you can't figure out why things don't work, you
may use @acronym{GPGME}'s built in trace feature.  This feature is
either enabled using the environment variable @code{GPGME_DEBUG} or,
if this is not possible, by calling the function
@code{gpgme_set_global_flag}.  The value is the trace level and
an optional file name.  If no file name is given the trace output is
printed to @code{stderr}.

@noindent
For example
@smallexample
GPGME_DEBUG=9:/home/<USER>/mygpgme.log
@end smallexample
@noindent
(Note that under Windows you use a semicolon in place of the colon to
separate the fields.)

A trace level of 9 is pretty verbose and thus you may want to start
off with a lower level.  The exact definition of the trace levels and
the output format may change with any release; you need to check the
source code for details.  In any case the trace log should be helpful
to understand what is going going on.  Warning: The trace log may
reveal sensitive details like passphrases or other data you use in
your application.  If you are asked to send a log file, make sure that
you run your tests only with play data.

The trace function makes use of gpgrt's logging function and thus the
special @file{socket://} and @file{tcp://} files may be used.  Because
this conflicts with the use of colons to separate fields, the following
hack is used: If the file name contains the string @code{^//} all
carets are replaced by colons.  For example to log to TCP port 42042
this can be used:
@smallexample
GPGME_DEBUG=5:tcp^//127.0.0.1^42042
@end smallexample

@node Deprecated Functions
@appendix Deprecated Functions
@cindex deprecated

For backward compatibility @acronym{GPGME} has a number of functions,
data types and constants which are deprecated and should not be used
anymore.  We document here those which are really old to help
understanding old code and to allow migration to their modern
counterparts.

@strong{Warning:} These interfaces will be removed in a future version
of @acronym{GPGME}.

@deftypefun void gpgme_key_release (@w{gpgme_key_t @var{key}})
The function @code{gpgme_key_release} is equivalent to
@code{gpgme_key_unref}.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_import_ext (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_data_t @var{keydata}}, @w{int *@var{nr}})
@since{0.3.9}

The function @code{gpgme_op_import_ext} is equivalent to:

@example
  gpgme_error_t err = gpgme_op_import (ctx, keydata);
  if (!err)
    @{
      gpgme_import_result_t result = gpgme_op_import_result (ctx);
      *nr = result->considered;
    @}
@end example
@end deftypefun


@deftp {Data type} {gpgme_error_t (*gpgme_edit_cb_t) @
   (@w{void *@var{handle}}, @
    @w{gpgme_status_code_t @var{status}}, @
    @w{const char *@var{args}}, @
    @w{int @var{fd}})}
@tindex gpgme_edit_cb_t
The @code{gpgme_edit_cb_t} type is the type of functions which
@acronym{GPGME} calls if it a key edit operation is on-going.  The
status code @var{status} and the argument line @var{args} are passed
through by @acronym{GPGME} from the crypto engine.  The file
descriptor @var{fd} is -1 for normal status messages.  If @var{status}
indicates a command rather than a status message, the response to the
command should be written to @var{fd}.  The @var{handle} is provided
by the user at start of operation.

The function should return @code{GPG_ERR_FALSE} if it did not handle
the status code, @code{0} for success, or any other error value.
@end deftp

@deftypefun gpgme_error_t gpgme_op_edit (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{key}}, @w{gpgme_edit_cb_t @var{fnc}}, @w{void *@var{handle}}, @w{gpgme_data_t @var{out}})
@since{0.3.9}

Note: This function is deprecated, please use
@code{gpgme_op_interact} instead.

The function @code{gpgme_op_edit} processes the key @var{KEY}
interactively, using the edit callback function @var{FNC} with the
handle @var{HANDLE}.  The callback is invoked for every status and
command request from the crypto engine.  The output of the crypto
engine is written to the data object @var{out}.

Note that the protocol between the callback function and the crypto
engine is specific to the crypto engine and no further support in
implementing this protocol correctly is provided by @acronym{GPGME}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
edit operation completes successfully, @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{key} is not a valid pointer, and any error returned
by the crypto engine or the edit callback handler.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_edit_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{key}}, @w{gpgme_edit_cb_t @var{fnc}}, @w{void *@var{handle}}, @w{gpgme_data_t @var{out}})

@since{0.3.9}

Note: This function is deprecated, please use
@code{gpgme_op_interact_start} instead.

The function @code{gpgme_op_edit_start} initiates a
@code{gpgme_op_edit} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation was started successfully, and @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{key} is not a valid pointer.
@end deftypefun


@deftypefun gpgme_error_t gpgme_op_card_edit (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{key}}, @w{gpgme_edit_cb_t @var{fnc}}, @w{void *@var{handle}}, @w{gpgme_data_t @var{out}})
Note: This function is deprecated, please use @code{gpgme_op_interact}
with the flag @code{GPGME_INTERACT_CARD} instead.

The function @code{gpgme_op_card_edit} is analogous to
@code{gpgme_op_edit}, but should be used to process the smart card corresponding to the key @var{key}.
@end deftypefun

@deftypefun gpgme_error_t gpgme_op_card_edit_start (@w{gpgme_ctx_t @var{ctx}}, @w{gpgme_key_t @var{key}}, @w{gpgme_edit_cb_t @var{fnc}}, @w{void *@var{handle}}, @w{gpgme_data_t @var{out}})
Note: This function is deprecated, please use @code{gpgme_op_interact_start}
with the flag @code{GPGME_INTERACT_CARD} instead.

The function @code{gpgme_op_card_edit_start} initiates a
@code{gpgme_op_card_edit} operation.  It can be completed by calling
@code{gpgme_wait} on the context.  @xref{Waiting For Completion}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
operation was started successfully, and @code{GPG_ERR_INV_VALUE} if
@var{ctx} or @var{key} is not a valid pointer.
@end deftypefun



@deftypefun gpgme_error_t gpgme_data_new_with_read_cb (@w{gpgme_data_t *@var{dh}}, @w{int (*@var{readfunc})} (@w{void *@var{hook}}, @w{char *@var{buffer}}, @w{size_t @var{count}}, @w{size_t *@var{nread}}), @w{void *@var{hook_value}})
The function @code{gpgme_data_new_with_read_cb} creates a new
@code{gpgme_data_t} object and uses the callback function @var{readfunc}
to retrieve the data on demand.  As the callback function can supply
the data in any way it wants, this is the most flexible data type
@acronym{GPGME} provides.  However, it can not be used to write data.

The callback function receives @var{hook_value} as its first argument
whenever it is invoked.  It should return up to @var{count} bytes in
@var{buffer}, and return the number of bytes actually read in
@var{nread}.  It may return @code{0} in @var{nread} if no data is
currently available.  To indicate @code{EOF} the function should
return with an error code of @code{-1} and set @var{nread} to
@code{0}.  The callback function may support to reset its internal
read pointer if it is invoked with @var{buffer} and @var{nread} being
@code{NULL} and @var{count} being @code{0}.

The function returns the error code @code{GPG_ERR_NO_ERROR} if the
data object was successfully created, @code{GPG_ERR_INV_VALUE} if
@var{dh} or @var{readfunc} is not a valid pointer, and
@code{GPG_ERR_ENOMEM} if not enough memory is available.
@end deftypefun

@deftypefun gpgme_error_t gpgme_data_rewind (@w{gpgme_data_t @var{dh}})
The function @code{gpgme_data_rewind} is equivalent to:

@example
  return (gpgme_data_seek (dh, 0, SEEK_SET) == -1)
    ? gpgme_error_from_errno (errno) : 0;
@end example
@end deftypefun


@c
@c Key Signatures
@c
The signatures on a key are only available if the key was retrieved
via a listing operation with the @code{GPGME_KEYLIST_MODE_SIGS} mode
enabled, because it is expensive to retrieve all signatures of a key.

So, before using the below interfaces to retrieve the signatures on a
key, you have to make sure that the key was listed with signatures
enabled.  One convenient, but blocking, way to do this is to use the
function @code{gpgme_get_key}.


@deftp {Data type} {enum gpgme_sig_stat_t}
@tindex gpgme_sig_stat_t
The @code{gpgme_sig_stat_t} type holds the result of a signature check, or
the combined result of all signatures.  The following results are
possible:

@table @code
@item GPGME_SIG_STAT_NONE
This status should not occur in normal operation.

@item GPGME_SIG_STAT_GOOD
This status indicates that the signature is valid.  For the combined
result this status means that all signatures are valid.

@item GPGME_SIG_STAT_GOOD_EXP
This status indicates that the signature is valid but expired.  For
the combined result this status means that all signatures are valid
and expired.

@item GPGME_SIG_STAT_GOOD_EXPKEY
This status indicates that the signature is valid but the key used to
verify the signature has expired.  For the combined result this status
means that all signatures are valid and all keys are expired.

@item GPGME_SIG_STAT_BAD
This status indicates that the signature is invalid.  For the combined
result this status means that all signatures are invalid.

@item GPGME_SIG_STAT_NOKEY
This status indicates that the signature could not be verified due to
a missing key.  For the combined result this status means that all
signatures could not be checked due to missing keys.

@item GPGME_SIG_STAT_NOSIG
This status indicates that the signature data provided was not a real
signature.

@item GPGME_SIG_STAT_ERROR
This status indicates that there was some other error which prevented
the signature verification.

@item GPGME_SIG_STAT_DIFF
For the combined result this status means that at least two signatures
have a different status.  You can get each key's status with
@code{gpgme_get_sig_status}.
@end table
@end deftp

@deftypefun {const char *} gpgme_get_sig_status (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{idx}}, @w{gpgme_sig_stat_t *@var{r_stat}}, @w{time_t *@var{r_created}})
The function @code{gpgme_get_sig_status} is equivalent to:

@example
  gpgme_verify_result_t result;
  gpgme_signature_t sig;

  result = gpgme_op_verify_result (ctx);
  sig = result->signatures;

  while (sig && idx)
    @{
      sig = sig->next;
      idx--;
    @}
  if (!sig || idx)
    return NULL;

  if (r_stat)
    @{
      switch (gpg_err_code (sig->status))
	@{
	case GPG_ERR_NO_ERROR:
	  *r_stat = GPGME_SIG_STAT_GOOD;
	  break;

	case GPG_ERR_BAD_SIGNATURE:
	  *r_stat = GPGME_SIG_STAT_BAD;
	  break;

	case GPG_ERR_NO_PUBKEY:
	  *r_stat = GPGME_SIG_STAT_NOKEY;
	  break;

	case GPG_ERR_NO_DATA:
	  *r_stat = GPGME_SIG_STAT_NOSIG;
	  break;

	case GPG_ERR_SIG_EXPIRED:
	  *r_stat = GPGME_SIG_STAT_GOOD_EXP;
	  break;

	case GPG_ERR_KEY_EXPIRED:
	  *r_stat = GPGME_SIG_STAT_GOOD_EXPKEY;
	  break;

	default:
	  *r_stat = GPGME_SIG_STAT_ERROR;
	  break;
	@}
    @}
  if (r_created)
    *r_created = sig->timestamp;
  return sig->fpr;
@end example
@end deftypefun


@deftypefun {const char *} gpgme_get_sig_key (@w{gpgme_ctx_t @var{ctx}}, @w{int @var{idx}}, @w{gpgme_key_t *@var{r_key}})
The function @code{gpgme_get_sig_key} is equivalent to:

@example
  gpgme_verify_result_t result;
  gpgme_signature_t sig;

  result = gpgme_op_verify_result (ctx);
  sig = result->signatures;

  while (sig && idx)
    @{
      sig = sig->next;
      idx--;
    @}
  if (!sig || idx)
    return gpg_error (GPG_ERR_EOF);

  return gpgme_get_key (ctx, sig->fpr, r_key, 0);
@end example
@end deftypefun


@include lesser.texi

@include gpl.texi

@node Concept Index
@unnumbered Concept Index

@printindex cp

@node Function and Data Index
@unnumbered Function and Data Index

@printindex fn


@bye
